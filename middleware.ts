import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

const WINDOW_MS = 60_000;  // 1 minute
const MAX = 5;             // 5 posts per minute per IP
const hits = new Map<string, { count: number; ts: number }>();

export function middleware(req: NextRequest) {
  if (req.nextUrl.pathname === "/api/contact" && req.method === "POST") {
    const ip =
      req.headers.get("x-real-ip") ||
      req.headers.get("x-forwarded-for")?.split(",")[0]?.trim() ||
      "0.0.0.0";
    const now = Date.now();
    const rec = hits.get(ip);

    if (!rec || now - rec.ts > WINDOW_MS) {
      hits.set(ip, { count: 1, ts: now });
    } else {
      rec.count++;
      if (rec.count > MAX) {
        // If form POST (HTML), bounce back to home with error
        const url = new URL(req.url);
        url.pathname = "/";
        url.searchParams.set("sent", "0");
        return NextResponse.redirect(url, { status: 303 });
      }
    }
  }
  return NextResponse.next();
}
export const config = { matcher: ["/api/contact"] };
