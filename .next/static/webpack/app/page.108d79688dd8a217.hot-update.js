"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Activity; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2\",\n            key: \"169zse\"\n        }\n    ]\n];\nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"activity\", __iconNode);\n //# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-check-2.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ FileCheck2; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\",\n            key: \"1pf5j1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m3 15 2 2 4-4\",\n            key: \"1lhrkk\"\n        }\n    ]\n];\nconst FileCheck2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-check-2\", __iconNode);\n //# sourceMappingURL=file-check-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(app-pages-browser)/./components/Hero.tsx\");\n/* harmony import */ var _components_BenefitsStrip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/BenefitsStrip */ \"(app-pages-browser)/./components/BenefitsStrip.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 25,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 26,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 27,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 27,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 28,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\n// Banner component with auto-dismiss\nconst Banner = (param)=>{\n    let { type, message, onDismiss } = param;\n    _s();\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm \".concat(isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Banner, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c9 = Banner;\nfunction Page() {\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BenefitsStrip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Free 30-min security assessment\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 194\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"),\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"LYEiLyDd1q+cBrk78YFHFw8zzvY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams\n    ];\n});\n_c10 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Banner\");\n$RefreshReg$(_c10, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ2Q7QUFDa0I7QUFDZDtBQUNBO0FBQ2U7QUFDK0g7QUFDckk7QUFNbEQsTUFBTXNCLFlBQVk7UUFBQyxFQUFFQyxZQUFZLEVBQUUsRUFBRUMsUUFBUSxFQUFtRDt5QkFDOUYsOERBQUNDO1FBQUlGLFdBQVcsaURBQTJELE9BQVZBO2tCQUFjQzs7Ozs7OztLQUQzRUY7QUFHTixNQUFNSSxVQUFVO1FBQUMsRUFBRUMsRUFBRSxFQUFFSixZQUFZLEVBQUUsRUFBRUMsUUFBUSxFQUFnRTt5QkFDN0csOERBQUNJO1FBQVFELElBQUlBO1FBQUlKLFdBQVcsa0JBQTRCLE9BQVZBO2tCQUFjQzs7Ozs7OztNQUR4REU7QUFHTixNQUFNRyxjQUFjO0lBQUVDLFFBQVE7UUFBRUMsU0FBUztRQUFHQyxHQUFHO0lBQUU7SUFBR0MsTUFBTTtRQUFFRixTQUFTO1FBQUdDLEdBQUc7UUFBR0UsWUFBWTtZQUFFQyxVQUFVO1FBQUk7SUFBRTtBQUFFO0FBQzlHLE1BQU1DLE1BQU07UUFBQyxFQUFFWixRQUFRLEVBQWlDO3lCQUFNLDhEQUFDYTtRQUFLZCxXQUFVO2tCQUF3SUM7Ozs7Ozs7TUFBaE5ZO0FBQ04sTUFBTUUsT0FBTztRQUFDLEVBQUVkLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNhO1FBQUtkLFdBQVU7a0JBQW1IQzs7Ozs7OztNQUE1TGM7QUFDTixNQUFNQyxRQUFRO1FBQUMsRUFBRWYsUUFBUSxFQUFpQzt5QkFBTSw4REFBQ2E7UUFBS2QsV0FBVTtrQkFBZ0hDOzs7Ozs7O01BQTFMZTtBQUNOLE1BQU1DLE9BQU87UUFBQyxFQUFFaEIsUUFBUSxFQUFFRCxZQUFZLEVBQUUsRUFBbUQ7eUJBQU0sOERBQUNFO1FBQUlGLFdBQVcsaUhBQTJILE9BQVZBO2tCQUFjQzs7Ozs7OztNQUExT2dCO0FBQ04sTUFBTUMsWUFBWTtRQUFDLEVBQUVqQixRQUFRLEVBQUVrQixJQUFJLEVBQXlEO3lCQUFNLDhEQUFDakI7UUFBSUYsV0FBVTs7WUFBaUVtQjswQkFBSyw4REFBQ0w7MEJBQU1iOzs7Ozs7Ozs7Ozs7O01BQXhMaUI7QUFDTixNQUFNRSxXQUFXO1FBQUMsRUFBRW5CLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNvQjtRQUFHckIsV0FBVTs7MEJBQStDLDhEQUFDUCw0TEFBWUE7Z0JBQUNPLFdBQVU7Ozs7OztZQUE2QkM7Ozs7Ozs7O01BQS9LbUI7QUFDTixNQUFNRSxXQUFXO1FBQUMsRUFBRXJCLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNDO1FBQUlGLFdBQVU7a0JBQXNEQzs7Ozs7OztNQUFsSXFCO0FBRU4scUNBQXFDO0FBQ3JDLE1BQU1DLFNBQVM7UUFBQyxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUF5RTs7SUFDakgsTUFBTUMsWUFBWUgsU0FBUztJQUUzQjdDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWlELFFBQVFDLFdBQVc7WUFDdkJIO1FBQ0YsR0FBRyxPQUFPLCtCQUErQjtRQUV6QyxPQUFPLElBQU1JLGFBQWFGO0lBQzVCLEdBQUc7UUFBQ0Y7S0FBVTtJQUVkLHFCQUNFLDhEQUFDMUMsaURBQU1BLENBQUNrQixHQUFHO1FBQ1Q2QixTQUFTO1lBQUV2QixTQUFTO1lBQUdDLEdBQUcsQ0FBQztZQUFJdUIsT0FBTztRQUFLO1FBQzNDQyxTQUFTO1lBQUV6QixTQUFTO1lBQUdDLEdBQUc7WUFBR3VCLE9BQU87UUFBRTtRQUN0Q0UsTUFBTTtZQUFFMUIsU0FBUztZQUFHQyxHQUFHLENBQUM7WUFBSXVCLE9BQU87UUFBSztRQUN4Q3JCLFlBQVk7WUFBRUMsVUFBVTtRQUFJO1FBQzVCWixXQUFXLDZJQUlWLE9BSEMyQixZQUNJLDZEQUNBO2tCQUdOLDRFQUFDekI7WUFBSUYsV0FBVTs7OEJBQ2IsOERBQUNFO29CQUFJRixXQUFVOzt3QkFDWjJCLDBCQUNDLDhEQUFDbEMsNExBQVlBOzRCQUFDTyxXQUFVOzs7OztzREFFeEIsOERBQUNILDRMQUFDQTs0QkFBQ0csV0FBVTs7Ozs7O3NDQUVmLDhEQUFDYzs0QkFBS2QsV0FBVTtzQ0FBZ0N5Qjs7Ozs7Ozs7Ozs7OzhCQUVsRCw4REFBQ1U7b0JBQ0NDLFNBQVNWO29CQUNUMUIsV0FBVTtvQkFDVnFDLGNBQVc7OEJBRVgsNEVBQUN4Qyw0TEFBQ0E7d0JBQUNHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLdkI7R0ExQ011QjtNQUFBQTtBQTRDUyxTQUFTZTs7SUFDdEIsTUFBTUMsZUFBZXpDLGdFQUFlQTtJQUNwQyxNQUFNMEMsVUFBVUQsYUFBYUUsR0FBRyxDQUFDO0lBRWpDLDBDQUEwQztJQUMxQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2pFLCtDQUFRQSxDQUF3RDtJQUM1RixNQUFNLENBQUNrRSxjQUFjQyxnQkFBZ0IsR0FBR25FLCtDQUFRQSxDQUFDO0lBRWpELHdEQUF3RDtJQUN4REMsZ0RBQVNBLENBQUM7UUFDUixJQUFJNkQsWUFBWSxLQUFLO1lBQ25CRyxVQUFVO2dCQUFFbkIsTUFBTTtnQkFBV0MsU0FBUztZQUF3RDtRQUNoRyxPQUFPLElBQUllLFlBQVksS0FBSztZQUMxQkcsVUFBVTtnQkFBRW5CLE1BQU07Z0JBQVNDLFNBQVM7WUFBaUQ7UUFDdkY7SUFDRixHQUFHO1FBQUNlO0tBQVE7SUFFWixnREFBZ0Q7SUFDaEQsTUFBTU0sZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUNoQkgsZ0JBQWdCO1FBQ2hCRixVQUFVLE9BQU8sNEJBQTRCO1FBRTdDLE1BQU1NLFdBQVcsSUFBSUMsU0FBU0gsRUFBRUksYUFBYTtRQUU3QyxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGdCQUFnQjtnQkFDM0NDLFFBQVE7Z0JBQ1JDLE1BQU1OO1lBQ1I7WUFFQSxJQUFJRyxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2ZiLFVBQVU7b0JBQUVuQixNQUFNO29CQUFXQyxTQUFTO2dCQUF3RDtnQkFDOUYsYUFBYTtnQkFDWnNCLEVBQUVVLE1BQU0sQ0FBcUJDLEtBQUs7WUFDckMsT0FBTztnQkFDTGYsVUFBVTtvQkFBRW5CLE1BQU07b0JBQVNDLFNBQVM7Z0JBQWlEO1lBQ3ZGO1FBQ0YsRUFBRSxPQUFPa0MsT0FBTztZQUNkaEIsVUFBVTtnQkFBRW5CLE1BQU07Z0JBQVNDLFNBQVM7WUFBNkQ7UUFDbkcsU0FBVTtZQUNSb0IsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNZSxnQkFBZ0I7UUFDcEJqQixVQUFVO0lBQ1o7SUFFQSxxQkFDRSw4REFBQ2tCO1FBQUt6RCxJQUFHOzswQkFFUCw4REFBQ25CLDJEQUFlQTswQkFDYnlELHdCQUNDLDhEQUFDbkI7b0JBQ0NDLE1BQU1rQixPQUFPbEIsSUFBSTtvQkFDakJDLFNBQVNpQixPQUFPakIsT0FBTztvQkFDdkJDLFdBQVdrQzs7Ozs7Ozs7Ozs7MEJBS2pCLDhEQUFDOUUsMERBQU1BOzs7OzswQkFDUCw4REFBQ0Ysd0RBQUlBOzs7OzswQkFDTCw4REFBQ0MsaUVBQWFBOzs7OzswQkFDZCw4REFBQ3FCO2dCQUFJRixXQUFVOzBCQUNiLDRFQUFDRztvQkFBUUgsV0FBVTs4QkFDakIsNEVBQUNEO2tDQUNDLDRFQUFDRzs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNoQixpREFBTUEsQ0FBQ2tCLEdBQUc7b0NBQUM2QixTQUFTO3dDQUFFdkIsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRztvQ0FBR3dCLFNBQVM7d0NBQUV6QixTQUFTO3dDQUFHQyxHQUFHO29DQUFFO29DQUFHRSxZQUFZO3dDQUFFQyxVQUFVO29DQUFJOztzREFDckcsOERBQUNDO3NEQUFJOzs7Ozs7c0RBQ0wsOERBQUNpRDs0Q0FBRzlELFdBQVU7c0RBQStEOzs7Ozs7c0RBQzdFLDhEQUFDK0Q7NENBQUUvRCxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMxQyw4REFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDZ0U7b0RBQUVDLE1BQUs7b0RBQVdqRSxXQUFVOzt3REFBbUg7c0VBQStCLDhEQUFDTiw2TEFBVUE7NERBQUNNLFdBQVU7Ozs7Ozs7Ozs7Ozs4REFDck0sOERBQUNnRTtvREFBRUMsTUFBSztvREFBV2pFLFdBQVU7OERBQXlIOzs7Ozs7OERBQ3RKLDhEQUFDRTtvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUNlOzs4RUFBSyw4REFBQ3hCLDZMQUFXQTtvRUFBQ1MsV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7OztzRUFDN0MsOERBQUNlOzs4RUFBSyw4REFBQzFCLDZMQUFLQTtvRUFBQ1csV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7OztzRUFDdkMsOERBQUNlOzs4RUFBSyw4REFBQ3BCLDZMQUFjQTtvRUFBQ0ssV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJdEQsOERBQUNoQixpREFBTUEsQ0FBQ2tCLEdBQUc7b0NBQUM2QixTQUFTO3dDQUFFdkIsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRztvQ0FBR3dCLFNBQVM7d0NBQUV6QixTQUFTO3dDQUFHQyxHQUFHO29DQUFFO29DQUFHRSxZQUFZO3dDQUFFQyxVQUFVO3dDQUFLc0QsT0FBTztvQ0FBSTs4Q0FDakgsNEVBQUNoRTt3Q0FBSUYsV0FBVTtrREFDYiw0RUFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDaUI7O3NFQUFLLDhEQUFDQzs0REFBVUMsb0JBQU0sOERBQUNqQyw2TEFBV0E7Z0VBQUNjLFdBQVU7Ozs7OztzRUFBK0I7Ozs7OztzRUFBNkIsOERBQUNtRTs0REFBR25FLFdBQVU7OzhFQUEwQiw4REFBQ29COzhFQUFTOzs7Ozs7OEVBQTJELDhEQUFDQTs4RUFBUzs7Ozs7OzhFQUF3Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFDblIsOERBQUNIOztzRUFBSyw4REFBQ0M7NERBQVVDLG9CQUFNLDhEQUFDaEMsNkxBQVFBO2dFQUFDYSxXQUFVOzs7Ozs7c0VBQTRCOzs7Ozs7c0VBQThCLDhEQUFDbUU7NERBQUduRSxXQUFVOzs4RUFBMEIsOERBQUNvQjs4RUFBUzs7Ozs7OzhFQUE0Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs4RUFBd0MsOERBQUNBOzhFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBQy9QLDhEQUFDSDtvREFBS2pCLFdBQVU7O3NFQUFhLDhEQUFDa0I7NERBQVVDLG9CQUFNLDhEQUFDL0IsNkxBQVNBO2dFQUFDWSxXQUFVOzs7Ozs7c0VBQStCOzs7Ozs7c0VBQThCLDhEQUFDbUU7NERBQUduRSxXQUFVOzs4RUFBNEIsOERBQUNvQjs4RUFBUzs7Ozs7OzhFQUF1Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs4RUFBNEMsOERBQUNBOzhFQUFTOzs7Ozs7OEVBQThDLDhEQUFDQTs4RUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFValcsOERBQUNqQjtnQkFBUUMsSUFBRzswQkFDViw0RUFBQ0w7O3NDQUNDLDhEQUFDRzs0QkFBSUYsV0FBVTs7OENBQWdDLDhEQUFDb0U7b0NBQUdwRSxXQUFVOzhDQUFnQzs7Ozs7OzhDQUFnQiw4REFBQytEO29DQUFFL0QsV0FBVTs4Q0FBcUI7Ozs7Ozs7Ozs7OztzQ0FDL0ksOERBQUNFOzRCQUFJRixXQUFVO3NDQUNaO2dDQUFDO29DQUFFcUUsT0FBTztvQ0FBMEJsRCxvQkFBTSw4REFBQ2hDLDZMQUFRQTt3Q0FBQ2EsV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBd0JsRCxvQkFBTSw4REFBQzdCLDZMQUFLQTt3Q0FBQ1UsV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBNEJsRCxvQkFBTSw4REFBQ3ZCLDZMQUFLQTt3Q0FBQ0ksV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBNkJsRCxvQkFBTSw4REFBQzNCLDZMQUFVQTt3Q0FBQ1EsV0FBVTs7Ozs7O2dDQUFhOzZCQUFFLENBQUNzRSxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3hULDhEQUFDeEYsaURBQU1BLENBQUNrQixHQUFHO29DQUFTNkIsU0FBUzt3Q0FBQ3ZCLFNBQVE7d0NBQUVDLEdBQUU7b0NBQUM7b0NBQUdnRSxhQUFhO3dDQUFDakUsU0FBUTt3Q0FBRUMsR0FBRTtvQ0FBQztvQ0FBR2lFLFVBQVU7d0NBQUVDLE1BQU07b0NBQUs7b0NBQUdoRSxZQUFZO3dDQUFDQyxVQUFTO29DQUFHO29DQUFHWixXQUFVOztzREFDMUksOERBQUNFOzRDQUFJRixXQUFVOztnREFBc0N1RSxFQUFFcEQsSUFBSTs4REFBQyw4REFBQ3lEO29EQUFHNUUsV0FBVTs4REFBeUJ1RSxFQUFFRixLQUFLOzs7Ozs7Ozs7Ozs7c0RBQzFHLDhEQUFDTjs0Q0FBRS9ELFdBQVU7c0RBQTZCOzs7Ozs7O21DQUYzQndFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3pCLDhEQUFDckU7Z0JBQVFDLElBQUc7Z0JBQVVKLFdBQVU7MEJBQzlCLDRFQUFDRDs7c0NBQ0MsOERBQUNHOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ29FO29DQUFHcEUsV0FBVTs4Q0FBZ0M7Ozs7Ozs4Q0FDOUMsOERBQUMrRDtvQ0FBRS9ELFdBQVU7OENBQXFCOzs7Ozs7Ozs7Ozs7c0NBRXBDLDhEQUFDRTs0QkFBSUYsV0FBVTtzQ0FDYiw0RUFBQzZFO2dDQUFLQyxVQUFVaEM7Z0NBQWM5QyxXQUFVOztrREFDdEMsOERBQUMrRTt3Q0FDQ0MsTUFBSzt3Q0FDTEMsUUFBUTt3Q0FDUmpGLFdBQVU7d0NBQ1ZrRixhQUFZOzs7Ozs7a0RBRWQsOERBQUNIO3dDQUNDQyxNQUFLO3dDQUNMeEQsTUFBSzt3Q0FDTHlELFFBQVE7d0NBQ1JqRixXQUFVO3dDQUNWa0YsYUFBWTs7Ozs7O2tEQUVkLDhEQUFDSDt3Q0FDQ0MsTUFBSzt3Q0FDTGhGLFdBQVU7d0NBQ1ZrRixhQUFZOzs7Ozs7a0RBRWQsOERBQUNDO3dDQUNDSCxNQUFLO3dDQUNMSSxNQUFNO3dDQUNOcEYsV0FBVTt3Q0FDVmtGLGFBQVk7Ozs7OztrREFFZCw4REFBQy9DO3dDQUNDWCxNQUFLO3dDQUNMNkQsVUFBVXpDO3dDQUNWNUMsV0FBVyxnS0FFVixPQURDNEMsZUFBZSxrQ0FBa0M7a0RBR2xEQSxlQUFlLGVBQWU7Ozs7OztrREFFakMsOERBQUNtQjt3Q0FBRS9ELFdBQVU7a0RBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU16RCw4REFBQ2pCLDBEQUFNQTs7Ozs7Ozs7Ozs7QUFHYjtJQWxLd0J1RDs7UUFDRHhDLDREQUFlQTs7O09BRGR3QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgSGVybyBmcm9tIFwiQC9jb21wb25lbnRzL0hlcm9cIjtcbmltcG9ydCBCZW5lZml0c1N0cmlwIGZyb20gXCJAL2NvbXBvbmVudHMvQmVuZWZpdHNTdHJpcFwiO1xuaW1wb3J0IE5hdmJhciBmcm9tIFwiQC9jb21wb25lbnRzL05hdmJhclwiO1xuaW1wb3J0IEZvb3RlciBmcm9tIFwiQC9jb21wb25lbnRzL0Zvb3RlclwiO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuaW1wb3J0IHsgU2hpZWxkQ2hlY2ssIEtleVJvdW5kLCBGaWxlQ2hlY2ssIFdhbmQyLCBHYXVnZSwgTG9ja0tleWhvbGUsIFNjcm9sbFRleHQsIENoZWNrQ2lyY2xlMiwgQXJyb3dSaWdodCwgQnVpbGRpbmcyLCBCb3hlcywgV29ya2Zsb3csIENsaXBib2FyZENoZWNrLCBSYWRhciwgWCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcblxuXG5cblxuXG5jb25zdCBDb250YWluZXIgPSAoeyBjbGFzc05hbWUgPSBcIlwiLCBjaGlsZHJlbiB9OiBSZWFjdC5Qcm9wc1dpdGhDaGlsZHJlbjx7IGNsYXNzTmFtZT86IHN0cmluZyB9PikgPT4gKFxuICA8ZGl2IGNsYXNzTmFtZT17YG14LWF1dG8gdy1mdWxsIG1heC13LTd4bCBweC00IHNtOnB4LTYgbGc6cHgtOCAke2NsYXNzTmFtZX1gfT57Y2hpbGRyZW59PC9kaXY+XG4pO1xuY29uc3QgU2VjdGlvbiA9ICh7IGlkLCBjbGFzc05hbWUgPSBcIlwiLCBjaGlsZHJlbiB9OiBSZWFjdC5Qcm9wc1dpdGhDaGlsZHJlbjx7IGlkPzogc3RyaW5nOyBjbGFzc05hbWU/OiBzdHJpbmcgfT4pID0+IChcbiAgPHNlY3Rpb24gaWQ9e2lkfSBjbGFzc05hbWU9e2BweS0xNiBzbTpweS0yNCAke2NsYXNzTmFtZX1gfT57Y2hpbGRyZW59PC9zZWN0aW9uPlxuKTtcbmNvbnN0IGZlYXR1cmVGYWRlID0geyBoaWRkZW46IHsgb3BhY2l0eTogMCwgeTogOCB9LCBzaG93OiB7IG9wYWNpdHk6IDEsIHk6IDAsIHRyYW5zaXRpb246IHsgZHVyYXRpb246IDAuNCB9IH0gfTtcbmNvbnN0IFRhZyA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHJvdW5kZWQtZnVsbCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIGJnLXdoaXRlLzUgcHgtMyBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC13aGl0ZS85MCBiYWNrZHJvcC1ibHVyLXNtXCI+e2NoaWxkcmVufTwvc3Bhbj4pO1xuY29uc3QgUGlsbCA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHJvdW5kZWQtZnVsbCBiZy16aW5jLTkwMC84MCBweC0zIHB5LTEgdGV4dC14cyB0ZXh0LXppbmMtMjAwIHJpbmctMSByaW5nLXdoaXRlLzEwXCI+e2NoaWxkcmVufTwvc3Bhbj4pO1xuY29uc3QgQmFkZ2UgPSAoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkgPT4gKDxzcGFuIGNsYXNzTmFtZT1cInJvdW5kZWQtZnVsbCBiZy1lbWVyYWxkLTYwMC8xNSBweC0yLjUgcHktMC41IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1lbWVyYWxkLTQwMCByaW5nLTEgcmluZy1lbWVyYWxkLTUwMC8zMFwiPntjaGlsZHJlbn08L3NwYW4+KTtcbmNvbnN0IENhcmQgPSAoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gXCJcIiB9OiBSZWFjdC5Qcm9wc1dpdGhDaGlsZHJlbjx7IGNsYXNzTmFtZT86IHN0cmluZyB9PikgPT4gKDxkaXYgY2xhc3NOYW1lPXtgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBiZy16aW5jLTkwMC82MCBwLTYgc2hhZG93LVswXzBfMF8xcHhfcmdiYSgyNTUsMjU1LDI1NSwwLjA0KV0gYmFja2Ryb3AtYmx1ciAke2NsYXNzTmFtZX1gfT57Y2hpbGRyZW59PC9kaXY+KTtcbmNvbnN0IENhcmRUaXRsZSA9ICh7IGNoaWxkcmVuLCBpY29uIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTsgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPGRpdiBjbGFzc05hbWU9XCJtYi0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e2ljb259PHNwYW4+e2NoaWxkcmVufTwvc3Bhbj48L2Rpdj4pO1xuY29uc3QgQ2FyZEl0ZW0gPSAoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkgPT4gKDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yIHRleHQtc20gdGV4dC16aW5jLTMwMFwiPjxDaGVja0NpcmNsZTIgY2xhc3NOYW1lPVwibXQtMC41IGgtNCB3LTQgc2hyaW5rLTBcIiAvPntjaGlsZHJlbn08L2xpPik7XG5jb25zdCBQcmljZVJvdyA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBnYXAtMSB0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPntjaGlsZHJlbn08L2Rpdj4pO1xuXG4vLyBCYW5uZXIgY29tcG9uZW50IHdpdGggYXV0by1kaXNtaXNzXG5jb25zdCBCYW5uZXIgPSAoeyB0eXBlLCBtZXNzYWdlLCBvbkRpc21pc3MgfTogeyB0eXBlOiAnc3VjY2VzcycgfCAnZXJyb3InOyBtZXNzYWdlOiBzdHJpbmc7IG9uRGlzbWlzczogKCkgPT4gdm9pZCB9KSA9PiB7XG4gIGNvbnN0IGlzU3VjY2VzcyA9IHR5cGUgPT09ICdzdWNjZXNzJztcbiAgXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIG9uRGlzbWlzcygpO1xuICAgIH0sIDUwMDApOyAvLyBBdXRvLWRpc21pc3MgYWZ0ZXIgNSBzZWNvbmRzXG4gICAgXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gIH0sIFtvbkRpc21pc3NdKTtcblxuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IC01MCwgc2NhbGU6IDAuOTUgfX1cbiAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCwgc2NhbGU6IDEgfX1cbiAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTUwLCBzY2FsZTogMC45NSB9fVxuICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XG4gICAgICBjbGFzc05hbWU9e2BmaXhlZCB0b3AtNCBsZWZ0LTEvMiB6LTUwIHctW2NhbGMoMTAwdnctMnJlbSldIG1heC13LW1kIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIHJvdW5kZWQteGwgYm9yZGVyIHB4LTQgcHktMyBzaGFkb3ctbGcgYmFja2Ryb3AtYmx1ci1zbSAke1xuICAgICAgICBpc1N1Y2Nlc3NcbiAgICAgICAgICA/ICdib3JkZXItZW1lcmFsZC01MDAvMzAgYmctZW1lcmFsZC01MDAvMTAgdGV4dC1lbWVyYWxkLTMwMCdcbiAgICAgICAgICA6ICdib3JkZXItcmVkLTUwMC8zMCBiZy1yZWQtNTAwLzEwIHRleHQtcmVkLTMwMCdcbiAgICAgIH1gfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdhcC0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICB7aXNTdWNjZXNzID8gKFxuICAgICAgICAgICAgPENoZWNrQ2lyY2xlMiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZW1lcmFsZC00MDAgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNDAwIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0cnVuY2F0ZVwiPnttZXNzYWdlfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtvbkRpc21pc3N9XG4gICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIHAtMSBob3ZlcjpiZy13aGl0ZS8xMCB0cmFuc2l0aW9uLWNvbG9ycyBmbGV4LXNocmluay0wXCJcbiAgICAgICAgICBhcmlhLWxhYmVsPVwiRGlzbWlzcyBub3RpZmljYXRpb25cIlxuICAgICAgICA+XG4gICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XG4gIGNvbnN0IHVybFNlbnQgPSBzZWFyY2hQYXJhbXMuZ2V0KFwic2VudFwiKTtcbiAgXG4gIC8vIFN0YXRlIGZvciBjbGllbnQtc2lkZSBiYW5uZXIgbWFuYWdlbWVudFxuICBjb25zdCBbYmFubmVyLCBzZXRCYW5uZXJdID0gdXNlU3RhdGU8eyB0eXBlOiAnc3VjY2VzcycgfCAnZXJyb3InOyBtZXNzYWdlOiBzdHJpbmcgfSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEhhbmRsZSBVUkwtYmFzZWQgYmFubmVycyAoZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVybFNlbnQgPT09IFwiMVwiKSB7XG4gICAgICBzZXRCYW5uZXIoeyB0eXBlOiAnc3VjY2VzcycsIG1lc3NhZ2U6ICdUaGFua3MhIFdlXFwnbGwgZ2V0IGJhY2sgdG8geW91IHdpdGhpbiAxIGJ1c2luZXNzIGRheS4nIH0pO1xuICAgIH0gZWxzZSBpZiAodXJsU2VudCA9PT0gXCIwXCIpIHtcbiAgICAgIHNldEJhbm5lcih7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6ICdTb3JyeSwgc29tZXRoaW5nIHdlbnQgd3JvbmcuIFBsZWFzZSB0cnkgYWdhaW4uJyB9KTtcbiAgICB9XG4gIH0sIFt1cmxTZW50XSk7XG5cbiAgLy8gSGFuZGxlIGZvcm0gc3VibWlzc2lvbiB3aXRoIGNsaWVudC1zaWRlIHN0YXRlXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQ8SFRNTEZvcm1FbGVtZW50PikgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XG4gICAgc2V0QmFubmVyKG51bGwpOyAvLyBDbGVhciBhbnkgZXhpc3RpbmcgYmFubmVyXG5cbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YShlLmN1cnJlbnRUYXJnZXQpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NvbnRhY3QnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBib2R5OiBmb3JtRGF0YSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgc2V0QmFubmVyKHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlOiAnVGhhbmtzISBXZVxcJ2xsIGdldCBiYWNrIHRvIHlvdSB3aXRoaW4gMSBidXNpbmVzcyBkYXkuJyB9KTtcbiAgICAgICAgLy8gUmVzZXQgZm9ybVxuICAgICAgICAoZS50YXJnZXQgYXMgSFRNTEZvcm1FbGVtZW50KS5yZXNldCgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0QmFubmVyKHsgdHlwZTogJ2Vycm9yJywgbWVzc2FnZTogJ1NvcnJ5LCBzb21ldGhpbmcgd2VudCB3cm9uZy4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXRCYW5uZXIoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiAnTmV0d29yayBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbiBhbmQgdHJ5IGFnYWluLicgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGRpc21pc3NCYW5uZXIgPSAoKSA9PiB7XG4gICAgc2V0QmFubmVyKG51bGwpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPG1haW4gaWQ9XCJtYWluXCI+XG4gICAgICB7LyogRml4ZWQgcG9zaXRpb24gYmFubmVyIGF0IHRvcCAqL31cbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIHtiYW5uZXIgJiYgKFxuICAgICAgICAgIDxCYW5uZXIgXG4gICAgICAgICAgICB0eXBlPXtiYW5uZXIudHlwZX0gXG4gICAgICAgICAgICBtZXNzYWdlPXtiYW5uZXIubWVzc2FnZX0gXG4gICAgICAgICAgICBvbkRpc21pc3M9e2Rpc21pc3NCYW5uZXJ9IFxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG4gICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgIFxuICAgICAgPE5hdmJhciAvPlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxCZW5lZml0c1N0cmlwIC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiBiZy1bcmFkaWFsLWdyYWRpZW50KDYwcmVtXzYwcmVtX2F0XzUwJV8tMjByZW0scmdiYSgxNiwxODUsMTI5LDAuMjUpLHRyYW5zcGFyZW50KSxyYWRpYWwtZ3JhZGllbnQoNjByZW1fNjByZW1fYXRfLTEwJV8tMTAlLHJnYmEoMzQsMjExLDIzOCwwLjI1KSx0cmFuc3BhcmVudCldXCI+XG4gICAgICAgIDxTZWN0aW9uIGNsYXNzTmFtZT1cInBiLTIwIHB0LTI0XCI+XG4gICAgICAgICAgPENvbnRhaW5lcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBpdGVtcy1jZW50ZXIgZ2FwLTEwIG1kOmdyaWQtY29scy0yXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTIgfX0gYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19IHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fT5cbiAgICAgICAgICAgICAgICA8VGFnPkFJICsgQ2xvdWQgU2VjdXJpdHkgQ29waWxvdCBmb3IgU01CczwvVGFnPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJtdC00IHRleHQtNHhsIGZvbnQtYm9sZCBsZWFkaW5nLXRpZ2h0IHRleHQtd2hpdGUgc206dGV4dC01eGxcIj5TZWN1cmUgeW91ciBBSSBhZ2VudHMgJiBjbG91ZCDigJQgd2l0aG91dCBoaXJpbmcgYSBzZWN1cml0eSB0ZWFtPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtbGcgdGV4dC16aW5jLTMwMFwiPkNsb3VkTmV4dEFJIHByb3RlY3RzIHlvdXIgQUkgYW5kIEdDUCB3b3JrbG9hZHMgZnJvbSBkYXRhIGxlYWtzLCBJQU0gbWlzY29uZmlncywgYW5kIGNvbXBsaWFuY2UgZmFpbHVyZXMuIEtleWxlc3MgYnkgZGVmYXVsdC4gU01CLWZyaWVuZGx5LiBQcm9kdWN0aW9uIHJlYWR5LjwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZmxleCBmbGV4LXdyYXAgaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI2NvbnRhY3RcIiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC14bCBiZy13aGl0ZSBweC01IHB5LTIuNSBmb250LXNlbWlib2xkIHRleHQtYmxhY2sgc2hhZG93IGhvdmVyOmJnLXppbmMtMTAwXCI+RnJlZSAzMC1taW4gc2VjdXJpdHkgYXNzZXNzbWVudDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPjwvYT5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvcHJvZHVjdFwiIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcHgtNSBweS0yLjUgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzVcIj5TZWUgaG93IGl0IHdvcmtzPC9hPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQteHMgdGV4dC16aW5jLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8UGlsbD48TG9ja0tleWhvbGUgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjVcIiAvPiBLZXlsZXNzIElBTSAoV0lGKTwvUGlsbD5cbiAgICAgICAgICAgICAgICAgICAgPFBpbGw+PFdhbmQyIGNsYXNzTmFtZT1cImgtMy41IHctMy41XCIgLz4gUHJvbXB0IEZpcmV3YWxsPC9QaWxsPlxuICAgICAgICAgICAgICAgICAgICA8UGlsbD48Q2xpcGJvYXJkQ2hlY2sgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjVcIiAvPiBDb21wbGlhbmNlIFBhY2tzPC9QaWxsPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXYgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAxMiB9fSBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX0gdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC4xIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcm91bmRlZC0zeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBiZy16aW5jLTk1MC82MCBwLTQgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0zIHNtOmdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPjxDYXJkVGl0bGUgaWNvbj17PFNoaWVsZENoZWNrIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1lbWVyYWxkLTQwMFwiIC8+fT5BSSBBZ2VudCBTZWN1cml0eTwvQ2FyZFRpdGxlPjx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC16aW5jLTMwMFwiPjxDYXJkSXRlbT5Qcm9tcHQgZmlyZXdhbGwgYmxvY2tzIGphaWxicmVha3MgJiBleGZpbHRyYXRpb248L0NhcmRJdGVtPjxDYXJkSXRlbT5QSUkvUEhJIHJlZGFjdGlvbiB2aWEgR0NQIERMUDwvQ2FyZEl0ZW0+PENhcmRJdGVtPlRvb2wgYWxsb3dsaXN0aW5nIGZvciBBUElzL0RCczwvQ2FyZEl0ZW0+PC91bD48L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPjxDYXJkVGl0bGUgaWNvbj17PEtleVJvdW5kIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1jeWFuLTQwMFwiIC8+fT5HQ1AgSUFNIEd1YXJkcmFpbHM8L0NhcmRUaXRsZT48dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtemluYy0zMDBcIj48Q2FyZEl0ZW0+V29ya2xvYWQgSWRlbnRpdHksIG5vIHN0YXRpYyBrZXlzPC9DYXJkSXRlbT48Q2FyZEl0ZW0+U2hvcnQtbGl2ZWQgcGVyLWFjdGlvbiB0b2tlbnM8L0NhcmRJdGVtPjxDYXJkSXRlbT5Eb3duc2NvcGluZyB2aWEgQ3JlZGVudGlhbCBBY2Nlc3MgQm91bmRhcmllczwvQ2FyZEl0ZW0+PC91bD48L0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImNvbC1zcGFuLTJcIj48Q2FyZFRpdGxlIGljb249ezxGaWxlQ2hlY2sgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWVtZXJhbGQtNDAwXCIgLz59PkNvbXBsaWFuY2UgQ29waWxvdDwvQ2FyZFRpdGxlPjx1bCBjbGFzc05hbWU9XCJncmlkIGdhcC0yIHNtOmdyaWQtY29scy0yXCI+PENhcmRJdGVtPkhhc2gtY2hhaW5lZCBldmlkZW5jZSBsZWRnZXI8L0NhcmRJdGVtPjxDYXJkSXRlbT5ISVBBQSwgU09DIDIsIFBJUEVEQSAxLWNsaWNrIFBERnM8L0NhcmRJdGVtPjxDYXJkSXRlbT5Qb2xpY3kgdmVyc2lvbmluZyAmIGRyaWZ0IGRldGVjdGlvbjwvQ2FyZEl0ZW0+PENhcmRJdGVtPkFwcHJvdmFsIHdvcmtmbG93czwvQ2FyZEl0ZW0+PC91bD48L0NhcmQ+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9Db250YWluZXI+XG4gICAgICAgIDwvU2VjdGlvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUHJvYmxlbS9Tb2x1dGlvbi9Ib3cvQ29tcGxpYW5jZS9QcmljaW5nIGZyb20gbGFuZGluZyAqL31cbiAgICAgIDxTZWN0aW9uIGlkPVwicHJvYmxlbVwiPlxuICAgICAgICA8Q29udGFpbmVyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy0zeGwgdGV4dC1jZW50ZXJcIj48aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5UaGUgUHJvYmxlbTwvaDI+PHAgY2xhc3NOYW1lPVwibXQtMyB0ZXh0LXppbmMtMzAwXCI+U01CcyB3YW50IEFJICYgY2xvdWQgc3BlZWQgYnV0IGNhbid0IGFmZm9yZCBzZWN1cml0eSB0ZWFtcy4gUmVzdWx0OiBzdGF0aWMga2V5cyBpbiByZXBvcywgb3Zlci1icm9hZCBJQU0sIHByb21wdCBpbmplY3Rpb24gcmlza3MsIGFuZCBhdWRpdCBmYWlsdXJlcy48L3A+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMCBncmlkIGdhcC00IHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgICB7W3sgdGl0bGU6IFwiU3RhdGljIGtleXMgZXZlcnl3aGVyZVwiLCBpY29uOiA8S2V5Um91bmQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IH0seyB0aXRsZTogXCJPdmVyLWJyb2FkIElBTSByb2xlc1wiLCBpY29uOiA8R2F1Z2UgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IH0seyB0aXRsZTogXCJQcm9tcHQgaW5qZWN0aW9uICYgZXhmaWxcIiwgaWNvbjogPFJhZGFyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPiB9LHsgdGl0bGU6IFwiQ29tcGxpYW5jZSBldmlkZW5jZSBjaGFvc1wiLCBpY29uOiA8U2Nyb2xsVGV4dCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gfV0ubWFwKChmLCBpKSA9PiAoXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IGtleT17aX0gaW5pdGlhbD17e29wYWNpdHk6MCx5Ojh9fSB3aGlsZUluVmlldz17e29wYWNpdHk6MSx5OjB9fSB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19IHRyYW5zaXRpb249e3tkdXJhdGlvbjowLjR9fSBjbGFzc05hbWU9XCJyb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTAwLzYwIHAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC13aGl0ZVwiPntmLmljb259PGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPntmLnRpdGxlfTwvaDM+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIHRleHQtemluYy0zMDBcIj5XZSByZXBsYWNlIGJyaXR0bGUgbWFudWFsIGNvbnRyb2xzIHdpdGggb3BpbmlvbmF0ZWQgZ3VhcmRyYWlscyBhbmQgYXV0b21hdGlvbiBkZXNpZ25lZCBmb3IgbGVhbiB0ZWFtcy48L3A+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgIDwvU2VjdGlvbj5cblxuICAgICAgPFNlY3Rpb24gaWQ9XCJjb250YWN0XCIgY2xhc3NOYW1lPVwiYmctemluYy05NTAvNjBcIj5cbiAgICAgICAgPENvbnRhaW5lcj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctM3hsIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5Cb29rIGEgRnJlZSBTZWN1cml0eSBBc3Nlc3NtZW50PC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTMgdGV4dC16aW5jLTMwMFwiPkdldCBhIHF1aWNrIGdhcCByZXBvcnQgb24gSUFNLCBwcm9tcHRzLCBhbmQgY29tcGxpYW5jZSByZWFkaW5lc3MgZm9yIHlvdXIgR0NQIHByb2plY3RzLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbXQtOCBtYXgtdy14bCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTAwLzYwIHAtNlwiPlxuICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNFwiPlxuICAgICAgICAgICAgICA8aW5wdXQgXG4gICAgICAgICAgICAgICAgbmFtZT1cIm5hbWVcIiBcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBiZy1ibGFjay80MCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyOnRleHQtemluYy01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWVtZXJhbGQtNTAwXCIgXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGdWxsIG5hbWVcIiBcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGlucHV0IFxuICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiIFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiIFxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLWJsYWNrLzQwIHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC16aW5jLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDBcIiBcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldvcmsgZW1haWxcIiBcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGlucHV0IFxuICAgICAgICAgICAgICAgIG5hbWU9XCJjb21wYW55XCIgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLWJsYWNrLzQwIHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC16aW5jLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDBcIiBcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbXBhbnlcIiBcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHRleHRhcmVhIFxuICAgICAgICAgICAgICAgIG5hbWU9XCJtZXNzYWdlXCIgXG4gICAgICAgICAgICAgICAgcm93cz17NH0gXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLWJsYWNrLzQwIHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC16aW5jLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDBcIiBcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRlbGwgdXMgYWJvdXQgeW91ciBBSS9jbG91ZCBzZXR1cFwiIFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWVtZXJhbGQtNTAwIHRvLWN5YW4tNTAwIHB4LTUgcHktMyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgdHJhbnNpdGlvbi1vcGFjaXR5ICR7XG4gICAgICAgICAgICAgICAgICBpc1N1Ym1pdHRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJ2hvdmVyOm9wYWNpdHktOTUnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gJ1NlbmRpbmcuLi4nIDogJ1JlcXVlc3QgQXNzZXNzbWVudCd9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LXhzIHRleHQtemluYy01MDBcIj5XZSdsbCByZXBseSB3aXRoaW4gMSBidXNpbmVzcyBkYXkuPC9wPlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgIDwvU2VjdGlvbj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSGVybyIsIkJlbmVmaXRzU3RyaXAiLCJOYXZiYXIiLCJGb290ZXIiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJTaGllbGRDaGVjayIsIktleVJvdW5kIiwiRmlsZUNoZWNrIiwiV2FuZDIiLCJHYXVnZSIsIkxvY2tLZXlob2xlIiwiU2Nyb2xsVGV4dCIsIkNoZWNrQ2lyY2xlMiIsIkFycm93UmlnaHQiLCJDbGlwYm9hcmRDaGVjayIsIlJhZGFyIiwiWCIsInVzZVNlYXJjaFBhcmFtcyIsIkNvbnRhaW5lciIsImNsYXNzTmFtZSIsImNoaWxkcmVuIiwiZGl2IiwiU2VjdGlvbiIsImlkIiwic2VjdGlvbiIsImZlYXR1cmVGYWRlIiwiaGlkZGVuIiwib3BhY2l0eSIsInkiLCJzaG93IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiVGFnIiwic3BhbiIsIlBpbGwiLCJCYWRnZSIsIkNhcmQiLCJDYXJkVGl0bGUiLCJpY29uIiwiQ2FyZEl0ZW0iLCJsaSIsIlByaWNlUm93IiwiQmFubmVyIiwidHlwZSIsIm1lc3NhZ2UiLCJvbkRpc21pc3MiLCJpc1N1Y2Nlc3MiLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJpbml0aWFsIiwic2NhbGUiLCJhbmltYXRlIiwiZXhpdCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwiUGFnZSIsInNlYXJjaFBhcmFtcyIsInVybFNlbnQiLCJnZXQiLCJiYW5uZXIiLCJzZXRCYW5uZXIiLCJpc1N1Ym1pdHRpbmciLCJzZXRJc1N1Ym1pdHRpbmciLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiY3VycmVudFRhcmdldCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJib2R5Iiwib2siLCJ0YXJnZXQiLCJyZXNldCIsImVycm9yIiwiZGlzbWlzc0Jhbm5lciIsIm1haW4iLCJoMSIsInAiLCJhIiwiaHJlZiIsImRlbGF5IiwidWwiLCJoMiIsInRpdGxlIiwibWFwIiwiZiIsImkiLCJ3aGlsZUluVmlldyIsInZpZXdwb3J0Iiwib25jZSIsImgzIiwiZm9ybSIsIm9uU3VibWl0IiwiaW5wdXQiLCJuYW1lIiwicmVxdWlyZWQiLCJwbGFjZWhvbGRlciIsInRleHRhcmVhIiwicm93cyIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/BenefitsStrip.tsx":
/*!**************************************!*\
  !*** ./components/BenefitsStrip.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BenefitsStrip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n// components/BenefitsStrip.tsx\n\n\nconst items = [\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Keyless by default\",\n        blurb: \"Short-lived tokens via Workload Identity Federation.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Least-privilege IAM\",\n        blurb: \"Downscoped access with continuous drift detection.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Audit & compliance\",\n        blurb: \"Exportable HIPAA • SOC 2 • PIPEDA evidence.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Production-ready\",\n        blurb: \"Signed builds, provenance, and OTel tracing.\"\n    }\n];\nfunction BenefitsStrip() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        \"aria-label\": \"Key benefits\",\n        className: \"border-y border-white/10 bg-zinc-950/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto grid w-full max-w-7xl grid-cols-1 gap-4 px-6 py-6 sm:grid-cols-2 lg:grid-cols-4 lg:gap-6 lg:py-8\",\n            children: items.map((param)=>{\n                let { icon: Icon, title, blurb } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3 rounded-2xl border border-white/10 bg-black/30 p-4 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-0.5 flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500/30 to-cyan-500/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"h-5 w-5 text-emerald-300\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-semibold text-white\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-zinc-300\",\n                                    children: blurb\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, title, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_c = BenefitsStrip;\nvar _c;\n$RefreshReg$(_c, \"BenefitsStrip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/BenefitsStrip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Hero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ClipboardCheck,FileCheck,KeyRound,LockKeyhole,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n// components/Hero.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Pill;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                lineNumber: 28,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = CardItem;\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 sm:py-24 pb-20 pt-24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid items-center gap-10 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 12\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                    children: \"AI + Cloud Security Copilot for SMBs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                    children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-lg text-zinc-300\",\n                                    children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#contact\",\n                                            className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                            children: [\n                                                \"Book a free security assessment \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 51\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/product\",\n                                            className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                            children: \"See how it works\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" Keyless IAM (WIF)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" Prompt Firewall\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" Compliance Packs\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 12\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        className: \"h-5 w-5 text-emerald-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 38\n                                                    }, void 0),\n                                                    children: \"AI Agent Security\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-zinc-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"PII/PHI redaction via GCP DLP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Tool allowlisting for APIs/DBs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-cyan-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 38\n                                                    }, void 0),\n                                                    children: \"GCP IAM Guardrails\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-zinc-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Workload Identity, no static keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Short-lived per-action tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Downscoping via Credential Access Boundaries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                            className: \"col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ClipboardCheck_FileCheck_KeyRound_LockKeyhole_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-emerald-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 38\n                                                    }, void 0),\n                                                    children: \"Compliance Copilot\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"grid gap-2 sm:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Hash-chained evidence ledger\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Policy versioning & drift detection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                            children: \"Approval workflows\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_c5 = Hero;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Tag\");\n$RefreshReg$(_c1, \"Pill\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"CardTitle\");\n$RefreshReg$(_c4, \"CardItem\");\n$RefreshReg$(_c5, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Hero.tsx\n"));

/***/ })

});