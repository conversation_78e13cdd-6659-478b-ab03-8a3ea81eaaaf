"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 25,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\n// Banner component with auto-dismiss\nconst Banner = (param)=>{\n    let { type, message, onDismiss } = param;\n    _s();\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm \".concat(isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Banner, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c9 = Banner;\nfunction Page() {\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Book a free security assessment \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 195\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"),\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"LYEiLyDd1q+cBrk78YFHFw8zzvY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams\n    ];\n});\n_c10 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Banner\");\n$RefreshReg$(_c10, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});