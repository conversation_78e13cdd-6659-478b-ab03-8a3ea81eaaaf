"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 18,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Book a free security assessment \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 38,\n                                                            columnNumber: 195\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 41,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 41,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 42,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 42,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 43,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 50,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 50,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 50,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 50,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 51,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 51,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 51,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 51,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 51,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 51,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 52,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 52,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 52,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 52,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 52,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 52,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 52,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can’t afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 140\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: [\n                                'const searchParams = useSearchParams(); const sent = searchParams.get(\"sent\"); // In the Contact section JSX, above the form container:',\n                                sent === \"1\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto mt-6 max-w-xl rounded-xl border border-emerald-500/30 bg-emerald-500/10 px-4 py-3 text-sm text-emerald-300\",\n                                    children: \"Thanks! We’ll get back to you within 1 business day.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 3\n                                }, this),\n                                sent === \"0\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto mt-6 max-w-xl rounded-xl border border-red-500/30 bg-red-500/10 px-4 py-3 text-sm text-red-300\",\n                                    children: \"Sorry, something went wrong. Please try again.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 3\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    action: \"/api/contact\",\n                                    method: \"post\",\n                                    className: \"grid gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            name: \"name\",\n                                            className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                            placeholder: \"Full name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            name: \"email\",\n                                            type: \"email\",\n                                            className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                            placeholder: \"Work email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            name: \"company\",\n                                            className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                            placeholder: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            name: \"message\",\n                                            rows: 4,\n                                            className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                            placeholder: \"Tell us about your AI/cloud setup\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white hover:opacity-95\",\n                                            children: \"Request Assessment\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center text-xs text-zinc-500\",\n                                            children: \"We’ll reply within 1 business day.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_c9 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzBCO0FBQ2U7QUFDQTtBQUNGO0FBQzZJO0FBSXBMLE1BQU1lLFlBQVk7UUFBQyxFQUFFQyxZQUFZLEVBQUUsRUFBRUMsUUFBUSxFQUFtRDt5QkFDOUYsOERBQUNDO1FBQUlGLFdBQVcsaURBQTJELE9BQVZBO2tCQUFjQzs7Ozs7OztLQUQzRUY7QUFHTixNQUFNSSxVQUFVO1FBQUMsRUFBRUMsRUFBRSxFQUFFSixZQUFZLEVBQUUsRUFBRUMsUUFBUSxFQUFnRTt5QkFDN0csOERBQUNJO1FBQVFELElBQUlBO1FBQUlKLFdBQVcsa0JBQTRCLE9BQVZBO2tCQUFjQzs7Ozs7OztNQUR4REU7QUFHTixNQUFNRyxjQUFjO0lBQUVDLFFBQVE7UUFBRUMsU0FBUztRQUFHQyxHQUFHO0lBQUU7SUFBR0MsTUFBTTtRQUFFRixTQUFTO1FBQUdDLEdBQUc7UUFBR0UsWUFBWTtZQUFFQyxVQUFVO1FBQUk7SUFBRTtBQUFFO0FBQzlHLE1BQU1DLE1BQU07UUFBQyxFQUFFWixRQUFRLEVBQWlDO3lCQUFNLDhEQUFDYTtRQUFLZCxXQUFVO2tCQUF3SUM7Ozs7Ozs7TUFBaE5ZO0FBQ04sTUFBTUUsT0FBTztRQUFDLEVBQUVkLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNhO1FBQUtkLFdBQVU7a0JBQW1IQzs7Ozs7OztNQUE1TGM7QUFDTixNQUFNQyxRQUFRO1FBQUMsRUFBRWYsUUFBUSxFQUFpQzt5QkFBTSw4REFBQ2E7UUFBS2QsV0FBVTtrQkFBZ0hDOzs7Ozs7O01BQTFMZTtBQUNOLE1BQU1DLE9BQU87UUFBQyxFQUFFaEIsUUFBUSxFQUFFRCxZQUFZLEVBQUUsRUFBbUQ7eUJBQU0sOERBQUNFO1FBQUlGLFdBQVcsaUhBQTJILE9BQVZBO2tCQUFjQzs7Ozs7OztNQUExT2dCO0FBQ04sTUFBTUMsWUFBWTtRQUFDLEVBQUVqQixRQUFRLEVBQUVrQixJQUFJLEVBQXlEO3lCQUFNLDhEQUFDakI7UUFBSUYsV0FBVTs7WUFBaUVtQjswQkFBSyw4REFBQ0w7MEJBQU1iOzs7Ozs7Ozs7Ozs7O01BQXhMaUI7QUFDTixNQUFNRSxXQUFXO1FBQUMsRUFBRW5CLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNvQjtRQUFHckIsV0FBVTs7MEJBQStDLDhEQUFDTCwwTEFBWUE7Z0JBQUNLLFdBQVU7Ozs7OztZQUE2QkM7Ozs7Ozs7O01BQS9LbUI7QUFDTixNQUFNRSxXQUFXO1FBQUMsRUFBRXJCLFFBQVEsRUFBaUM7eUJBQU0sOERBQUNDO1FBQUlGLFdBQVU7a0JBQXNEQzs7Ozs7OztNQUFsSXFCO0FBRVMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDOzswQkFDQyw4REFBQ3ZDLDBEQUFNQTs7Ozs7MEJBQ1AsOERBQUNpQjtnQkFBSUYsV0FBVTswQkFDYiw0RUFBQ0c7b0JBQVFILFdBQVU7OEJBQ2pCLDRFQUFDRDtrQ0FDQyw0RUFBQ0c7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDYixpREFBTUEsQ0FBQ2UsR0FBRztvQ0FBQ3VCLFNBQVM7d0NBQUVqQixTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUFHaUIsU0FBUzt3Q0FBRWxCLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUU7b0NBQUdFLFlBQVk7d0NBQUVDLFVBQVU7b0NBQUk7O3NEQUNyRyw4REFBQ0M7c0RBQUk7Ozs7OztzREFDTCw4REFBQ2M7NENBQUczQixXQUFVO3NEQUErRDs7Ozs7O3NEQUM3RSw4REFBQzRCOzRDQUFFNUIsV0FBVTtzREFBNkI7Ozs7OztzREFDMUMsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQzZCO29EQUFFQyxNQUFLO29EQUFXOUIsV0FBVTs7d0RBQW1IO3NFQUFnQyw4REFBQ0osMExBQVVBOzREQUFDSSxXQUFVOzs7Ozs7Ozs7Ozs7OERBQ3RNLDhEQUFDNkI7b0RBQUVDLE1BQUs7b0RBQVc5QixXQUFVOzhEQUF5SDs7Ozs7OzhEQUN0Siw4REFBQ0U7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDZTs7OEVBQUssOERBQUN0QiwwTEFBV0E7b0VBQUNPLFdBQVU7Ozs7OztnRUFBZ0I7Ozs7Ozs7c0VBQzdDLDhEQUFDZTs7OEVBQUssOERBQUN4QiwwTEFBS0E7b0VBQUNTLFdBQVU7Ozs7OztnRUFBZ0I7Ozs7Ozs7c0VBQ3ZDLDhEQUFDZTs7OEVBQUssOERBQUNsQiwwTEFBY0E7b0VBQUNHLFdBQVU7Ozs7OztnRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSXRELDhEQUFDYixpREFBTUEsQ0FBQ2UsR0FBRztvQ0FBQ3VCLFNBQVM7d0NBQUVqQixTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUFHaUIsU0FBUzt3Q0FBRWxCLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUU7b0NBQUdFLFlBQVk7d0NBQUVDLFVBQVU7d0NBQUttQixPQUFPO29DQUFJOzhDQUNqSCw0RUFBQzdCO3dDQUFJRixXQUFVO2tEQUNiLDRFQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNpQjs7c0VBQUssOERBQUNDOzREQUFVQyxvQkFBTSw4REFBQy9CLDJMQUFXQTtnRUFBQ1ksV0FBVTs7Ozs7O3NFQUErQjs7Ozs7O3NFQUE2Qiw4REFBQ2dDOzREQUFHaEMsV0FBVTs7OEVBQTBCLDhEQUFDb0I7OEVBQVM7Ozs7Ozs4RUFBMkQsOERBQUNBOzhFQUFTOzs7Ozs7OEVBQXdDLDhEQUFDQTs4RUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUNuUiw4REFBQ0g7O3NFQUFLLDhEQUFDQzs0REFBVUMsb0JBQU0sOERBQUM5QiwyTEFBUUE7Z0VBQUNXLFdBQVU7Ozs7OztzRUFBNEI7Ozs7OztzRUFBOEIsOERBQUNnQzs0REFBR2hDLFdBQVU7OzhFQUEwQiw4REFBQ29COzhFQUFTOzs7Ozs7OEVBQTRDLDhEQUFDQTs4RUFBUzs7Ozs7OzhFQUF3Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFDL1AsOERBQUNIO29EQUFLakIsV0FBVTs7c0VBQWEsOERBQUNrQjs0REFBVUMsb0JBQU0sOERBQUM3QiwyTEFBU0E7Z0VBQUNVLFdBQVU7Ozs7OztzRUFBK0I7Ozs7OztzRUFBOEIsOERBQUNnQzs0REFBR2hDLFdBQVU7OzhFQUE0Qiw4REFBQ29COzhFQUFTOzs7Ozs7OEVBQXVDLDhEQUFDQTs4RUFBUzs7Ozs7OzhFQUE0Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs4RUFBOEMsOERBQUNBOzhFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVVqVyw4REFBQ2pCO2dCQUFRQyxJQUFHOzBCQUNWLDRFQUFDTDs7c0NBQ0MsOERBQUNHOzRCQUFJRixXQUFVOzs4Q0FBZ0MsOERBQUNpQztvQ0FBR2pDLFdBQVU7OENBQWdDOzs7Ozs7OENBQWdCLDhEQUFDNEI7b0NBQUU1QixXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7O3NDQUMvSSw4REFBQ0U7NEJBQUlGLFdBQVU7c0NBQ1o7Z0NBQUM7b0NBQUVrQyxPQUFPO29DQUEwQmYsb0JBQU0sOERBQUM5QiwyTEFBUUE7d0NBQUNXLFdBQVU7Ozs7OztnQ0FBYTtnQ0FBRTtvQ0FBRWtDLE9BQU87b0NBQXdCZixvQkFBTSw4REFBQzNCLDJMQUFLQTt3Q0FBQ1EsV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFa0MsT0FBTztvQ0FBNEJmLG9CQUFNLDhEQUFDckIsMkxBQUtBO3dDQUFDRSxXQUFVOzs7Ozs7Z0NBQWE7Z0NBQUU7b0NBQUVrQyxPQUFPO29DQUE2QmYsb0JBQU0sOERBQUN6QiwyTEFBVUE7d0NBQUNNLFdBQVU7Ozs7OztnQ0FBYTs2QkFBRSxDQUFDbUMsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUN4VCw4REFBQ2xELGlEQUFNQSxDQUFDZSxHQUFHO29DQUFTdUIsU0FBUzt3Q0FBQ2pCLFNBQVE7d0NBQUVDLEdBQUU7b0NBQUM7b0NBQUc2QixhQUFhO3dDQUFDOUIsU0FBUTt3Q0FBRUMsR0FBRTtvQ0FBQztvQ0FBRzhCLFVBQVU7d0NBQUVDLE1BQU07b0NBQUs7b0NBQUc3QixZQUFZO3dDQUFDQyxVQUFTO29DQUFHO29DQUFHWixXQUFVOztzREFDMUksOERBQUNFOzRDQUFJRixXQUFVOztnREFBc0NvQyxFQUFFakIsSUFBSTs4REFBQyw4REFBQ3NCO29EQUFHekMsV0FBVTs4REFBeUJvQyxFQUFFRixLQUFLOzs7Ozs7Ozs7Ozs7c0RBQzFHLDhEQUFDTjs0Q0FBRTVCLFdBQVU7c0RBQTZCOzs7Ozs7O21DQUYzQnFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3pCLDhEQUFDbEM7Z0JBQVFDLElBQUc7Z0JBQVVKLFdBQVU7MEJBQzlCLDRFQUFDRDs7c0NBQ0MsOERBQUNHOzRCQUFJRixXQUFVOzs4Q0FBZ0MsOERBQUNpQztvQ0FBR2pDLFdBQVU7OENBQWdDOzs7Ozs7OENBQW9DLDhEQUFDNEI7b0NBQUU1QixXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7O3NDQUNuSyw4REFBQ0U7NEJBQUlGLFdBQVU7O2dDQUE4RTtnQ0FNdEcwQyxTQUFTLHFCQUNSLDhEQUFDeEM7b0NBQUlGLFdBQVU7OENBQXFIOzs7Ozs7Z0NBSXJJMEMsU0FBUyxxQkFDUiw4REFBQ3hDO29DQUFJRixXQUFVOzhDQUF5Rzs7Ozs7OzhDQUs5Ryw4REFBQzJDO29DQUFLQyxRQUFPO29DQUFlQyxRQUFPO29DQUFPN0MsV0FBVTs7c0RBQ2xELDhEQUFDOEM7NENBQU1DLE1BQUs7NENBQU8vQyxXQUFVOzRDQUE4SmdELGFBQVk7Ozs7OztzREFDdk0sOERBQUNGOzRDQUFNQyxNQUFLOzRDQUFRRSxNQUFLOzRDQUFRakQsV0FBVTs0Q0FBOEpnRCxhQUFZOzs7Ozs7c0RBQ3JOLDhEQUFDRjs0Q0FBTUMsTUFBSzs0Q0FBVS9DLFdBQVU7NENBQThKZ0QsYUFBWTs7Ozs7O3NEQUMxTSw4REFBQ0U7NENBQVNILE1BQUs7NENBQVVJLE1BQU07NENBQUduRCxXQUFVOzRDQUE4SmdELGFBQVk7Ozs7OztzREFDdE4sOERBQUNJOzRDQUFPcEQsV0FBVTtzREFBNko7Ozs7OztzREFDL0ssOERBQUM0Qjs0Q0FBRTVCLFdBQVU7c0RBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNekQsOERBQUNkLDBEQUFNQTs7Ozs7Ozs7Ozs7QUFHYjtNQXRGd0JxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBOYXZiYXIgZnJvbSBcIkAvY29tcG9uZW50cy9OYXZiYXJcIjtcbmltcG9ydCBGb290ZXIgZnJvbSBcIkAvY29tcG9uZW50cy9Gb290ZXJcIjtcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gXCJmcmFtZXItbW90aW9uXCI7XG5pbXBvcnQgeyBTaGllbGRDaGVjaywgS2V5Um91bmQsIEZpbGVDaGVjaywgV2FuZDIsIEdhdWdlLCBMb2NrS2V5aG9sZSwgU2Nyb2xsVGV4dCwgQ2hlY2tDaXJjbGUyLCBBcnJvd1JpZ2h0LCBCdWlsZGluZzIsIEJveGVzLCBXb3JrZmxvdywgQ2xpcGJvYXJkQ2hlY2ssIFJhZGFyIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuXG5cbmNvbnN0IENvbnRhaW5lciA9ICh7IGNsYXNzTmFtZSA9IFwiXCIsIGNoaWxkcmVuIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+KSA9PiAoXG4gIDxkaXYgY2xhc3NOYW1lPXtgbXgtYXV0byB3LWZ1bGwgbWF4LXctN3hsIHB4LTQgc206cHgtNiBsZzpweC04ICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L2Rpdj5cbik7XG5jb25zdCBTZWN0aW9uID0gKHsgaWQsIGNsYXNzTmFtZSA9IFwiXCIsIGNoaWxkcmVuIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgaWQ/OiBzdHJpbmc7IGNsYXNzTmFtZT86IHN0cmluZyB9PikgPT4gKFxuICA8c2VjdGlvbiBpZD17aWR9IGNsYXNzTmFtZT17YHB5LTE2IHNtOnB5LTI0ICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L3NlY3Rpb24+XG4pO1xuY29uc3QgZmVhdHVyZUZhZGUgPSB7IGhpZGRlbjogeyBvcGFjaXR5OiAwLCB5OiA4IH0sIHNob3c6IHsgb3BhY2l0eTogMSwgeTogMCwgdHJhbnNpdGlvbjogeyBkdXJhdGlvbjogMC40IH0gfSB9O1xuY29uc3QgVGFnID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItd2hpdGUvMjAgYmctd2hpdGUvNSBweC0zIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzkwIGJhY2tkcm9wLWJsdXItc21cIj57Y2hpbGRyZW59PC9zcGFuPik7XG5jb25zdCBQaWxsID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC1mdWxsIGJnLXppbmMtOTAwLzgwIHB4LTMgcHktMSB0ZXh0LXhzIHRleHQtemluYy0yMDAgcmluZy0xIHJpbmctd2hpdGUvMTBcIj57Y2hpbGRyZW59PC9zcGFuPik7XG5jb25zdCBCYWRnZSA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPHNwYW4gY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGJnLWVtZXJhbGQtNjAwLzE1IHB4LTIuNSBweS0wLjUgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWVtZXJhbGQtNDAwIHJpbmctMSByaW5nLWVtZXJhbGQtNTAwLzMwXCI+e2NoaWxkcmVufTwvc3Bhbj4pO1xuY29uc3QgQ2FyZCA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSBcIlwiIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+KSA9PiAoPGRpdiBjbGFzc05hbWU9e2Byb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTAwLzYwIHAtNiBzaGFkb3ctWzBfMF8wXzFweF9yZ2JhKDI1NSwyNTUsMjU1LDAuMDQpXSBiYWNrZHJvcC1ibHVyICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L2Rpdj4pO1xuY29uc3QgQ2FyZFRpdGxlID0gKHsgY2hpbGRyZW4sIGljb24gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlOyBpY29uPzogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8ZGl2IGNsYXNzTmFtZT1cIm1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57aWNvbn08c3Bhbj57Y2hpbGRyZW59PC9zcGFuPjwvZGl2Pik7XG5jb25zdCBDYXJkSXRlbSA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTIgdGV4dC1zbSB0ZXh0LXppbmMtMzAwXCI+PENoZWNrQ2lyY2xlMiBjbGFzc05hbWU9XCJtdC0wLjUgaC00IHctNCBzaHJpbmstMFwiIC8+e2NoaWxkcmVufTwvbGk+KTtcbmNvbnN0IFByaWNlUm93ID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIGdhcC0xIHRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+e2NoaWxkcmVufTwvZGl2Pik7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPG1haW4+XG4gICAgICA8TmF2YmFyIC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiBiZy1bcmFkaWFsLWdyYWRpZW50KDYwcmVtXzYwcmVtX2F0XzUwJV8tMjByZW0scmdiYSgxNiwxODUsMTI5LDAuMjUpLHRyYW5zcGFyZW50KSxyYWRpYWwtZ3JhZGllbnQoNjByZW1fNjByZW1fYXRfLTEwJV8tMTAlLHJnYmEoMzQsMjExLDIzOCwwLjI1KSx0cmFuc3BhcmVudCldXCI+XG4gICAgICAgIDxTZWN0aW9uIGNsYXNzTmFtZT1cInBiLTIwIHB0LTI0XCI+XG4gICAgICAgICAgPENvbnRhaW5lcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBpdGVtcy1jZW50ZXIgZ2FwLTEwIG1kOmdyaWQtY29scy0yXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTIgfX0gYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19IHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fT5cbiAgICAgICAgICAgICAgICA8VGFnPkFJICsgQ2xvdWQgU2VjdXJpdHkgQ29waWxvdCBmb3IgU01CczwvVGFnPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJtdC00IHRleHQtNHhsIGZvbnQtYm9sZCBsZWFkaW5nLXRpZ2h0IHRleHQtd2hpdGUgc206dGV4dC01eGxcIj5TZWN1cmUgeW91ciBBSSBhZ2VudHMgJiBjbG91ZCDigJQgd2l0aG91dCBoaXJpbmcgYSBzZWN1cml0eSB0ZWFtPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtbGcgdGV4dC16aW5jLTMwMFwiPkNsb3VkTmV4dEFJIHByb3RlY3RzIHlvdXIgQUkgYW5kIEdDUCB3b3JrbG9hZHMgZnJvbSBkYXRhIGxlYWtzLCBJQU0gbWlzY29uZmlncywgYW5kIGNvbXBsaWFuY2UgZmFpbHVyZXMuIEtleWxlc3MgYnkgZGVmYXVsdC4gU01CLWZyaWVuZGx5LiBQcm9kdWN0aW9uIHJlYWR5LjwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZmxleCBmbGV4LXdyYXAgaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI2NvbnRhY3RcIiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC14bCBiZy13aGl0ZSBweC01IHB5LTIuNSBmb250LXNlbWlib2xkIHRleHQtYmxhY2sgc2hhZG93IGhvdmVyOmJnLXppbmMtMTAwXCI+Qm9vayBhIGZyZWUgc2VjdXJpdHkgYXNzZXNzbWVudCA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz48L2E+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiL3Byb2R1Y3RcIiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHB4LTUgcHktMi41IGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS81XCI+U2VlIGhvdyBpdCB3b3JrczwvYT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXhzIHRleHQtemluYy00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPFBpbGw+PExvY2tLZXlob2xlIGNsYXNzTmFtZT1cImgtMy41IHctMy41XCIgLz4gS2V5bGVzcyBJQU0gKFdJRik8L1BpbGw+XG4gICAgICAgICAgICAgICAgICAgIDxQaWxsPjxXYW5kMiBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNVwiIC8+IFByb21wdCBGaXJld2FsbDwvUGlsbD5cbiAgICAgICAgICAgICAgICAgICAgPFBpbGw+PENsaXBib2FyZENoZWNrIGNsYXNzTmFtZT1cImgtMy41IHctMy41XCIgLz4gQ29tcGxpYW5jZSBQYWNrczwvUGlsbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTIgfX0gYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19IHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMSB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHJvdW5kZWQtM3hsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctemluYy05NTAvNjAgcC00IHNoYWRvdy0yeGxcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD48Q2FyZFRpdGxlIGljb249ezxTaGllbGRDaGVjayBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZW1lcmFsZC00MDBcIiAvPn0+QUkgQWdlbnQgU2VjdXJpdHk8L0NhcmRUaXRsZT48dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtemluYy0zMDBcIj48Q2FyZEl0ZW0+UHJvbXB0IGZpcmV3YWxsIGJsb2NrcyBqYWlsYnJlYWtzICYgZXhmaWx0cmF0aW9uPC9DYXJkSXRlbT48Q2FyZEl0ZW0+UElJL1BISSByZWRhY3Rpb24gdmlhIEdDUCBETFA8L0NhcmRJdGVtPjxDYXJkSXRlbT5Ub29sIGFsbG93bGlzdGluZyBmb3IgQVBJcy9EQnM8L0NhcmRJdGVtPjwvdWw+PC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD48Q2FyZFRpdGxlIGljb249ezxLZXlSb3VuZCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtY3lhbi00MDBcIiAvPn0+R0NQIElBTSBHdWFyZHJhaWxzPC9DYXJkVGl0bGU+PHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXppbmMtMzAwXCI+PENhcmRJdGVtPldvcmtsb2FkIElkZW50aXR5LCBubyBzdGF0aWMga2V5czwvQ2FyZEl0ZW0+PENhcmRJdGVtPlNob3J0LWxpdmVkIHBlci1hY3Rpb24gdG9rZW5zPC9DYXJkSXRlbT48Q2FyZEl0ZW0+RG93bnNjb3BpbmcgdmlhIENyZWRlbnRpYWwgQWNjZXNzIEJvdW5kYXJpZXM8L0NhcmRJdGVtPjwvdWw+PC9DYXJkPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJjb2wtc3Bhbi0yXCI+PENhcmRUaXRsZSBpY29uPXs8RmlsZUNoZWNrIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1lbWVyYWxkLTQwMFwiIC8+fT5Db21wbGlhbmNlIENvcGlsb3Q8L0NhcmRUaXRsZT48dWwgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtMiBzbTpncmlkLWNvbHMtMlwiPjxDYXJkSXRlbT5IYXNoLWNoYWluZWQgZXZpZGVuY2UgbGVkZ2VyPC9DYXJkSXRlbT48Q2FyZEl0ZW0+SElQQUEsIFNPQyAyLCBQSVBFREEgMS1jbGljayBQREZzPC9DYXJkSXRlbT48Q2FyZEl0ZW0+UG9saWN5IHZlcnNpb25pbmcgJiBkcmlmdCBkZXRlY3Rpb248L0NhcmRJdGVtPjxDYXJkSXRlbT5BcHByb3ZhbCB3b3JrZmxvd3M8L0NhcmRJdGVtPjwvdWw+PC9DYXJkPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ29udGFpbmVyPlxuICAgICAgICA8L1NlY3Rpb24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByb2JsZW0vU29sdXRpb24vSG93L0NvbXBsaWFuY2UvUHJpY2luZyBmcm9tIGxhbmRpbmcgKi99XG4gICAgICA8U2VjdGlvbiBpZD1cInByb2JsZW1cIj5cbiAgICAgICAgPENvbnRhaW5lcj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctM3hsIHRleHQtY2VudGVyXCI+PGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+VGhlIFByb2JsZW08L2gyPjxwIGNsYXNzTmFtZT1cIm10LTMgdGV4dC16aW5jLTMwMFwiPlNNQnMgd2FudCBBSSAmIGNsb3VkIHNwZWVkIGJ1dCBjYW7igJl0IGFmZm9yZCBzZWN1cml0eSB0ZWFtcy4gUmVzdWx0OiBzdGF0aWMga2V5cyBpbiByZXBvcywgb3Zlci1icm9hZCBJQU0sIHByb21wdCBpbmplY3Rpb24gcmlza3MsIGFuZCBhdWRpdCBmYWlsdXJlcy48L3A+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMCBncmlkIGdhcC00IHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgICB7W3sgdGl0bGU6IFwiU3RhdGljIGtleXMgZXZlcnl3aGVyZVwiLCBpY29uOiA8S2V5Um91bmQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IH0seyB0aXRsZTogXCJPdmVyLWJyb2FkIElBTSByb2xlc1wiLCBpY29uOiA8R2F1Z2UgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IH0seyB0aXRsZTogXCJQcm9tcHQgaW5qZWN0aW9uICYgZXhmaWxcIiwgaWNvbjogPFJhZGFyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPiB9LHsgdGl0bGU6IFwiQ29tcGxpYW5jZSBldmlkZW5jZSBjaGFvc1wiLCBpY29uOiA8U2Nyb2xsVGV4dCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gfV0ubWFwKChmLCBpKSA9PiAoXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IGtleT17aX0gaW5pdGlhbD17e29wYWNpdHk6MCx5Ojh9fSB3aGlsZUluVmlldz17e29wYWNpdHk6MSx5OjB9fSB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19IHRyYW5zaXRpb249e3tkdXJhdGlvbjowLjR9fSBjbGFzc05hbWU9XCJyb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTAwLzYwIHAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC13aGl0ZVwiPntmLmljb259PGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPntmLnRpdGxlfTwvaDM+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIHRleHQtemluYy0zMDBcIj5XZSByZXBsYWNlIGJyaXR0bGUgbWFudWFsIGNvbnRyb2xzIHdpdGggb3BpbmlvbmF0ZWQgZ3VhcmRyYWlscyBhbmQgYXV0b21hdGlvbiBkZXNpZ25lZCBmb3IgbGVhbiB0ZWFtcy48L3A+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgIDwvU2VjdGlvbj5cblxuICAgICAgPFNlY3Rpb24gaWQ9XCJjb250YWN0XCIgY2xhc3NOYW1lPVwiYmctemluYy05NTAvNjBcIj5cbiAgICAgICAgPENvbnRhaW5lcj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctM3hsIHRleHQtY2VudGVyXCI+PGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+Qm9vayBhIEZyZWUgU2VjdXJpdHkgQXNzZXNzbWVudDwvaDI+PHAgY2xhc3NOYW1lPVwibXQtMyB0ZXh0LXppbmMtMzAwXCI+R2V0IGEgcXVpY2sgZ2FwIHJlcG9ydCBvbiBJQU0sIHByb21wdHMsIGFuZCBjb21wbGlhbmNlIHJlYWRpbmVzcyBmb3IgeW91ciBHQ1AgcHJvamVjdHMuPC9wPjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtdC04IG1heC13LXhsIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctemluYy05MDAvNjAgcC02XCI+XG4gICAgICAgICAgXG5jb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbmNvbnN0IHNlbnQgPSBzZWFyY2hQYXJhbXMuZ2V0KFwic2VudFwiKTtcblxuLy8gSW4gdGhlIENvbnRhY3Qgc2VjdGlvbiBKU1gsIGFib3ZlIHRoZSBmb3JtIGNvbnRhaW5lcjpcbntzZW50ID09PSBcIjFcIiAmJiAoXG4gIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtdC02IG1heC13LXhsIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1lbWVyYWxkLTUwMC8zMCBiZy1lbWVyYWxkLTUwMC8xMCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LWVtZXJhbGQtMzAwXCI+XG4gICAgVGhhbmtzISBXZeKAmWxsIGdldCBiYWNrIHRvIHlvdSB3aXRoaW4gMSBidXNpbmVzcyBkYXkuXG4gIDwvZGl2PlxuKX1cbntzZW50ID09PSBcIjBcIiAmJiAoXG4gIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtdC02IG1heC13LXhsIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1yZWQtNTAwLzMwIGJnLXJlZC01MDAvMTAgcHgtNCBweS0zIHRleHQtc20gdGV4dC1yZWQtMzAwXCI+XG4gICAgU29ycnksIHNvbWV0aGluZyB3ZW50IHdyb25nLiBQbGVhc2UgdHJ5IGFnYWluLlxuICA8L2Rpdj5cbil9XG5cbiAgICAgICAgICAgIDxmb3JtIGFjdGlvbj1cIi9hcGkvY29udGFjdFwiIG1ldGhvZD1cInBvc3RcIiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxpbnB1dCBuYW1lPVwibmFtZVwiIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBiZy1ibGFjay80MCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyOnRleHQtemluYy01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWVtZXJhbGQtNTAwXCIgcGxhY2Vob2xkZXI9XCJGdWxsIG5hbWVcIiAvPlxuICAgICAgICAgICAgICA8aW5wdXQgbmFtZT1cImVtYWlsXCIgdHlwZT1cImVtYWlsXCIgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLWJsYWNrLzQwIHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC16aW5jLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDBcIiBwbGFjZWhvbGRlcj1cIldvcmsgZW1haWxcIiAvPlxuICAgICAgICAgICAgICA8aW5wdXQgbmFtZT1cImNvbXBhbnlcIiBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctYmxhY2svNDAgcHgtNCBweS0zIHRleHQtc20gdGV4dC13aGl0ZSBwbGFjZWhvbGRlcjp0ZXh0LXppbmMtNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1lbWVyYWxkLTUwMFwiIHBsYWNlaG9sZGVyPVwiQ29tcGFueVwiIC8+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYSBuYW1lPVwibWVzc2FnZVwiIHJvd3M9ezR9IGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBiZy1ibGFjay80MCBweC00IHB5LTMgdGV4dC1zbSB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyOnRleHQtemluYy01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWVtZXJhbGQtNTAwXCIgcGxhY2Vob2xkZXI9XCJUZWxsIHVzIGFib3V0IHlvdXIgQUkvY2xvdWQgc2V0dXBcIiAvPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTUwMCB0by1jeWFuLTUwMCBweC01IHB5LTMgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGhvdmVyOm9wYWNpdHktOTVcIj5SZXF1ZXN0IEFzc2Vzc21lbnQ8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC14cyB0ZXh0LXppbmMtNTAwXCI+V2XigJlsbCByZXBseSB3aXRoaW4gMSBidXNpbmVzcyBkYXkuPC9wPlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgIDwvU2VjdGlvbj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIk5hdmJhciIsIkZvb3RlciIsIm1vdGlvbiIsIlNoaWVsZENoZWNrIiwiS2V5Um91bmQiLCJGaWxlQ2hlY2siLCJXYW5kMiIsIkdhdWdlIiwiTG9ja0tleWhvbGUiLCJTY3JvbGxUZXh0IiwiQ2hlY2tDaXJjbGUyIiwiQXJyb3dSaWdodCIsIkNsaXBib2FyZENoZWNrIiwiUmFkYXIiLCJDb250YWluZXIiLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsImRpdiIsIlNlY3Rpb24iLCJpZCIsInNlY3Rpb24iLCJmZWF0dXJlRmFkZSIsImhpZGRlbiIsIm9wYWNpdHkiLCJ5Iiwic2hvdyIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsIlRhZyIsInNwYW4iLCJQaWxsIiwiQmFkZ2UiLCJDYXJkIiwiQ2FyZFRpdGxlIiwiaWNvbiIsIkNhcmRJdGVtIiwibGkiLCJQcmljZVJvdyIsIlBhZ2UiLCJtYWluIiwiaW5pdGlhbCIsImFuaW1hdGUiLCJoMSIsInAiLCJhIiwiaHJlZiIsImRlbGF5IiwidWwiLCJoMiIsInRpdGxlIiwibWFwIiwiZiIsImkiLCJ3aGlsZUluVmlldyIsInZpZXdwb3J0Iiwib25jZSIsImgzIiwic2VudCIsImZvcm0iLCJhY3Rpb24iLCJtZXRob2QiLCJpbnB1dCIsIm5hbWUiLCJwbGFjZWhvbGRlciIsInR5cGUiLCJ0ZXh0YXJlYSIsInJvd3MiLCJidXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});