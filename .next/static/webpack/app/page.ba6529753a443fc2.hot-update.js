"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\n// Banner component with auto-dismiss\nconst Banner = (param)=>{\n    let { type, message, onDismiss } = param;\n    _s();\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm \".concat(isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Banner, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c2 = Banner;\nfunction Page() {\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsStrip, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"),\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-org\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"Organization\",\n                        \"name\": \"CloudNextAI\",\n                        \"url\": \"https://www.cloudnext.ai\",\n                        \"logo\": \"/favicon.svg\",\n                        \"sameAs\": [\n                            \"https://www.linkedin.com/company/cloudnextai\"\n                        ]\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-product\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"SoftwareApplication\",\n                        \"name\": \"CloudNextAI\",\n                        \"applicationCategory\": \"SecurityApplication\",\n                        \"operatingSystem\": \"Cloud\",\n                        \"offers\": {\n                            \"@type\": \"Offer\",\n                            \"price\": \"499\",\n                            \"priceCurrency\": \"USD\"\n                        }\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 205,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"LYEiLyDd1q+cBrk78YFHFw8zzvY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams\n    ];\n});\n_c3 = Page;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Banner\");\n$RefreshReg$(_c3, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});