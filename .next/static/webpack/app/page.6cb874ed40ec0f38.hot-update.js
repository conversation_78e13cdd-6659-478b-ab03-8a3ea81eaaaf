"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js":
/*!*********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clipboard-check.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ClipboardCheck; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"8\",\n            height: \"4\",\n            x: \"8\",\n            y: \"2\",\n            rx: \"1\",\n            ry: \"1\",\n            key: \"tgr4d6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\",\n            key: \"116196\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 14 2 2 4-4\",\n            key: \"df797q\"\n        }\n    ]\n];\nconst ClipboardCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"clipboard-check\", __iconNode);\n //# sourceMappingURL=clipboard-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-check.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ FileCheck; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 15 2 2 4-4\",\n            key: \"1grp1n\"\n        }\n    ]\n];\nconst FileCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-check\", __iconNode);\n //# sourceMappingURL=file-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ LockKeyhole; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"16\",\n            r: \"1\",\n            key: \"1au0dj\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"3\",\n            y: \"10\",\n            width: \"18\",\n            height: \"12\",\n            rx: \"2\",\n            key: \"6s8ecr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 10V7a5 5 0 0 1 10 0v3\",\n            key: \"1pqi11\"\n        }\n    ]\n];\nconst LockKeyhole = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"lock-keyhole\", __iconNode);\n //# sourceMappingURL=lock-keyhole.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ WandSparkles; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.542.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72\",\n            key: \"ul74o6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m14 7 3 3\",\n            key: \"1r5n42\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 6v4\",\n            key: \"ilb8ba\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 14v4\",\n            key: \"blhpug\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 2v2\",\n            key: \"7u0qdc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 8H3\",\n            key: \"zfb6yr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 16h-4\",\n            key: \"1cnmox\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M11 3H9\",\n            key: \"1obp7u\"\n        }\n    ]\n];\nconst WandSparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wand-sparkles\", __iconNode);\n //# sourceMappingURL=wand-sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 25,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\n// Banner component with auto-dismiss\nconst Banner = (param)=>{\n    let { type, message, onDismiss } = param;\n    _s();\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm \".concat(isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Banner, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c9 = Banner;\nfunction Page() {\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Book a free security assessment \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 195\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"),\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-org\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"Organization\",\n                        \"name\": \"CloudNextAI\",\n                        \"url\": \"https://www.cloudnext.ai\",\n                        \"logo\": \"/favicon.svg\",\n                        \"sameAs\": [\n                            \"https://www.linkedin.com/company/cloudnextai\"\n                        ]\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-product\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"SoftwareApplication\",\n                        \"name\": \"CloudNextAI\",\n                        \"applicationCategory\": \"SecurityApplication\",\n                        \"operatingSystem\": \"Cloud\",\n                        \"offers\": {\n                            \"@type\": \"Offer\",\n                            \"price\": \"499\",\n                            \"priceCurrency\": \"USD\"\n                        }\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 242,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"LYEiLyDd1q+cBrk78YFHFw8zzvY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams\n    ];\n});\n_c10 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Banner\");\n$RefreshReg$(_c10, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});