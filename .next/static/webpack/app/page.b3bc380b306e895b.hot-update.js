"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzPzMwYWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 18,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\n// inside the Page() component\nconst searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\nconst sent = searchParams.get(\"sent\");\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Book a free security assessment \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 195\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 49,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 49,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 50,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 51,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 51,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 58,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 58,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 58,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 58,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 59,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 59,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 59,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 59,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 60,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 60,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 60,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 60,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 60,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can’t afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 140\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                action: \"/api/contact\",\n                                method: \"post\",\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white hover:opacity-95\",\n                                        children: \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We’ll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c9 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});