"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle2,Gauge,KeyRound,Radar,ScrollText,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst Container = (param)=>{\n    let { className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined);\n};\n_c = Container;\nconst Section = (param)=>{\n    let { id, className = \"\", children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: \"py-16 sm:py-24 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 63\n    }, undefined);\n};\n_c2 = Tag;\nconst Pill = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 64\n    }, undefined);\n};\n_c3 = Pill;\nconst Badge = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 65\n    }, undefined);\n};\n_c4 = Badge;\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 98\n    }, undefined);\n};\n_c5 = Card;\nconst CardTitle = (param)=>{\n    let { children, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 99\n    }, undefined);\n};\n_c6 = CardTitle;\nconst CardItem = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 68\n    }, undefined);\n};\n_c7 = CardItem;\nconst PriceRow = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 25,\n        columnNumber: 68\n    }, undefined);\n};\n_c8 = PriceRow;\n// Banner component with auto-dismiss\nconst Banner = (param)=>{\n    let { type, message, onDismiss } = param;\n    _s();\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm \".concat(isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Banner, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c9 = Banner;\nfunction Page() {\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Hero, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BenefitsStrip, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle2_Gauge_KeyRound_Radar_ScrollText_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity \".concat(isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"),\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-org\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"Organization\",\n                        \"name\": \"CloudNextAI\",\n                        \"url\": \"https://www.cloudnext.ai\",\n                        \"logo\": \"/favicon.svg\",\n                        \"sameAs\": [\n                            \"https://www.linkedin.com/company/cloudnextai\"\n                        ]\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Script, {\n                id: \"ld-product\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"SoftwareApplication\",\n                        \"name\": \"CloudNextAI\",\n                        \"applicationCategory\": \"SecurityApplication\",\n                        \"operatingSystem\": \"Cloud\",\n                        \"offers\": {\n                            \"@type\": \"Offer\",\n                            \"price\": \"499\",\n                            \"priceCurrency\": \"USD\"\n                        }\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s1(Page, \"LYEiLyDd1q+cBrk78YFHFw8zzvY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams\n    ];\n});\n_c10 = Page;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"Tag\");\n$RefreshReg$(_c3, \"Pill\");\n$RefreshReg$(_c4, \"Badge\");\n$RefreshReg$(_c5, \"Card\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardItem\");\n$RefreshReg$(_c8, \"PriceRow\");\n$RefreshReg$(_c9, \"Banner\");\n$RefreshReg$(_c10, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});