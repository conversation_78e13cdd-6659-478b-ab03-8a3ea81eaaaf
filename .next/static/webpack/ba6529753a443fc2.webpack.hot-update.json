{"c": ["app/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/BenefitsStrip.tsx", "(app-pages-browser)/./components/Hero.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js", "(app-pages-browser)/./node_modules/next/dist/api/script.js", "(app-pages-browser)/./node_modules/next/dist/client/head-manager.js", "(app-pages-browser)/./node_modules/next/dist/client/script.js"]}