/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYW50b255YWxwaG9uc2UlMkZEZXNrdG9wJTJGQW50b255JTJGQ29tcGFueSUyRmNsb3VkbmV4dGFpLXdlYnNpdGUlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQWtIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xvdWRuZXh0YWktc2l0ZS8/NTVlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbnRvbnlhbHBob25zZS9EZXNrdG9wL0FudG9ueS9Db21wYW55L2Nsb3VkbmV4dGFpLXdlYnNpdGUvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Container = ({ className = \"\", children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\nconst Section = ({ id, className = \"\", children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: `py-16 sm:py-24 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 63\n    }, undefined);\nconst Pill = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 18,\n        columnNumber: 64\n    }, undefined);\nconst Badge = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 65\n    }, undefined);\nconst Card = ({ children, className = \"\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 98\n    }, undefined);\nconst CardTitle = ({ children, icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 21,\n        columnNumber: 99\n    }, undefined);\nconst CardItem = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 68\n    }, undefined);\nconst PriceRow = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 68\n    }, undefined);\n// Banner component with auto-dismiss\nconst Banner = ({ type, message, onDismiss })=>{\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: `fixed top-4 left-1/2 z-50 mx-auto w-full max-w-md -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm ${isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"ml-2 rounded-full p-1 hover:bg-white/10 transition-colors\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\nfunction Page() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Book a free security assessment \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 195\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: `inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity ${isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"}`,\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 grid gap-6 py-10 sm:grid-cols-2 md:grid-cols-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 11,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold tracking-wide\",\n                                    children: \"CloudNextAI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-3 text-sm text-zinc-400\",\n                            children: \"AI + Cloud Security Copilot for SMBs in Windsor & Detroit.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Product\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/product\",\n                                        className: \"hover:text-white\",\n                                        children: \"AI Firewall\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/product\",\n                                        className: \"hover:text-white\",\n                                        children: \"IAM Guardrails\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/product\",\n                                        className: \"hover:text-white\",\n                                        children: \"Compliance Copilot\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Company\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"hover:text-white\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/#contact\",\n                                        className: \"hover:text-white\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 53\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"hover:text-white\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 81\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 53\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"hover:text-white\",\n                                            children: \"+1 (555) 123‑4567\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 82\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 53\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"hover:text-white\",\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 83\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 53\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"hover:text-white\",\n                                            children: \"LinkedIn\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 85\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:col-span-2 md:col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-xs text-zinc-500\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" CloudNextAI. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 border-b border-white/10 bg-black/60 backdrop-blur-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 flex h-16 items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"flex items-center gap-2 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-5 w-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-semibold tracking-wide\",\n                            children: \"CloudNextAI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"hidden gap-6 text-sm text-zinc-300 md:flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/product\",\n                            className: \"hover:text-white\",\n                            children: \"Product\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/solutions\",\n                            className: \"hover:text-white\",\n                            children: \"Solutions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/pricing\",\n                            className: \"hover:text-white\",\n                            children: \"Pricing\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/docs\",\n                            className: \"hover:text-white\",\n                            children: \"Docs\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/security\",\n                            className: \"hover:text-white\",\n                            children: \"Security\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: \"/#contact\",\n                        className: \"inline-flex items-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-4 py-2 text-sm font-semibold text-white shadow hover:opacity-95\",\n                        children: [\n                            \"Book a free assessment \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 36\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3e2f9963d4d8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbG91ZG5leHRhaS1zaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2ZkMjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzZTJmOTk2M2Q0ZDhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    metadataBase: new URL(\"https://www.cloudnext.ai\"),\n    title: {\n        default: \"CloudNextAI — AI + Cloud Security Copilot for SMBs\",\n        template: \"%s | CloudNextAI\"\n    },\n    description: \"Secure your AI agents & GCP cloud without hiring a security team.\",\n    openGraph: {\n        title: \"CloudNextAI — AI + Cloud Security Copilot for SMBs\",\n        description: \"Secure your AI agents & GCP cloud without hiring a security team.\",\n        url: \"https://www.cloudnext.ai\",\n        siteName: \"CloudNextAI\",\n        images: [\n            {\n                url: \"/og.png\",\n                width: 1200,\n                height: 630\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-black text-zinc-100\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();