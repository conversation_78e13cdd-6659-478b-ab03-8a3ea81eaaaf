/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYW50b255YWxwaG9uc2UlMkZEZXNrdG9wJTJGQW50b255JTJGQ29tcGFueSUyRmNsb3VkbmV4dGFpLXdlYnNpdGUlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQWtIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xvdWRuZXh0YWktc2l0ZS8/NTVlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbnRvbnlhbHBob25zZS9EZXNrdG9wL0FudG9ueS9Db21wYW55L2Nsb3VkbmV4dGFpLXdlYnNpdGUvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_BenefitsStrip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/BenefitsStrip */ \"(ssr)/./components/BenefitsStrip.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./components/Footer.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock-keyhole.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/radar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle2,ClipboardCheck,FileCheck,Gauge,KeyRound,LockKeyhole,Radar,ScrollText,ShieldCheck,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scroll-text.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst Container = ({ className = \"\", children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\nconst Section = ({ id, className = \"\", children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: `py-16 sm:py-24 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\nconst featureFade = {\n    hidden: {\n        opacity: 0,\n        y: 8\n    },\n    show: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.4\n        }\n    }\n};\nconst Tag = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 22,\n        columnNumber: 63\n    }, undefined);\nconst Pill = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 64\n    }, undefined);\nconst Badge = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 65\n    }, undefined);\nconst Card = ({ children, className = \"\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 25,\n        columnNumber: 98\n    }, undefined);\nconst CardTitle = ({ children, icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-2 flex items-center gap-2 text-lg font-semibold text-white\",\n        children: [\n            icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 184\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 26,\n        columnNumber: 99\n    }, undefined);\nconst CardItem = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"flex items-start gap-2 text-sm text-zinc-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"mt-0.5 h-4 w-4 shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 27,\n                columnNumber: 129\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 27,\n        columnNumber: 68\n    }, undefined);\nconst PriceRow = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-1 text-4xl font-bold text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 28,\n        columnNumber: 68\n    }, undefined);\n// Banner component with auto-dismiss\nconst Banner = ({ type, message, onDismiss })=>{\n    const isSuccess = type === \"success\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onDismiss();\n        }, 5000); // Auto-dismiss after 5 seconds\n        return ()=>clearTimeout(timer);\n    }, [\n        onDismiss\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            y: -50,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: `fixed top-4 left-1/2 z-50 w-[calc(100vw-2rem)] max-w-md transform -translate-x-1/2 rounded-xl border px-4 py-3 shadow-lg backdrop-blur-sm ${isSuccess ? \"border-emerald-500/30 bg-emerald-500/10 text-emerald-300\" : \"border-red-500/30 bg-red-500/10 text-red-300\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 min-w-0 flex-1\",\n                    children: [\n                        isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4 text-emerald-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4 text-red-400 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium truncate\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"rounded-full p-1 hover:bg-white/10 transition-colors flex-shrink-0\",\n                    \"aria-label\": \"Dismiss notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\nfunction Page() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const urlSent = searchParams.get(\"sent\");\n    // State for client-side banner management\n    const [banner, setBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL-based banners (for backward compatibility)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (urlSent === \"1\") {\n            setBanner({\n                type: \"success\",\n                message: \"Thanks! We'll get back to you within 1 business day.\"\n            });\n        } else if (urlSent === \"0\") {\n            setBanner({\n                type: \"error\",\n                message: \"Sorry, something went wrong. Please try again.\"\n            });\n        }\n    }, [\n        urlSent\n    ]);\n    // Handle form submission with client-side state\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setBanner(null); // Clear any existing banner\n        const formData = new FormData(e.currentTarget);\n        try {\n            const response = await fetch(\"/api/contact\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (response.ok) {\n                setBanner({\n                    type: \"success\",\n                    message: \"Thanks! We'll get back to you within 1 business day.\"\n                });\n                // Reset form\n                e.target.reset();\n            } else {\n                setBanner({\n                    type: \"error\",\n                    message: \"Sorry, something went wrong. Please try again.\"\n                });\n            }\n        } catch (error) {\n            setBanner({\n                type: \"error\",\n                message: \"Network error. Please check your connection and try again.\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const dismissBanner = ()=>{\n        setBanner(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        id: \"main\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: banner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {\n                    type: banner.type,\n                    message: banner.message,\n                    onDismiss: dismissBanner\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BenefitsStrip__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                    className: \"pb-20 pt-24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid items-center gap-10 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                                            children: \"AI + Cloud Security Copilot for SMBs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl\",\n                                            children: \"Secure your AI agents & cloud — without hiring a security team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-zinc-300\",\n                                            children: \"CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex flex-wrap items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100\",\n                                                    children: [\n                                                        \"Free 30-min security assessment\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 194\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/product\",\n                                                    className: \"inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5\",\n                                                    children: \"See how it works\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-2 flex items-center gap-2 text-xs text-zinc-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Keyless IAM (WIF)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Prompt Firewall\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Pill, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3.5 w-3.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \" Compliance Packs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 12\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3 sm:gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"AI Agent Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Prompt firewall blocks jailbreaks & exfiltration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 167\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"PII/PHI redaction via GCP DLP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 236\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Tool allowlisting for APIs/DBs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 286\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 127\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 44\n                                                            }, void 0),\n                                                            children: \"GCP IAM Guardrails\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2 text-zinc-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Workload Identity, no static keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 162\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Short-lived per-action tokens\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 216\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Downscoping via Credential Access Boundaries\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 266\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 122\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 67\n                                                            }, void 0),\n                                                            children: \"Compliance Copilot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 50\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"grid gap-2 sm:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Hash-chained evidence ledger\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 191\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"HIPAA, SOC 2, PIPEDA 1-click PDFs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 240\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Policy versioning & drift detection\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 294\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardItem, {\n                                                                    children: \"Approval workflows\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 350\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 149\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"problem\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"The Problem\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 58\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"SMBs want AI & cloud speed but can't afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 120\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                            children: [\n                                {\n                                    title: \"Static keys everywhere\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 56\n                                    }, this)\n                                },\n                                {\n                                    title: \"Over-broad IAM roles\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 130\n                                    }, this)\n                                },\n                                {\n                                    title: \"Prompt injection & exfil\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 205\n                                    }, this)\n                                },\n                                {\n                                    title: \"Compliance evidence chaos\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle2_ClipboardCheck_FileCheck_Gauge_KeyRound_LockKeyhole_Radar_ScrollText_ShieldCheck_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 281\n                                    }, this)\n                                }\n                            ].map((f, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-white\",\n                                            children: [\n                                                f.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: f.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 77\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-zinc-300\",\n                                            children: \"We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                id: \"contact\",\n                className: \"bg-zinc-950/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Container, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Book a Free Security Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-3 text-zinc-300\",\n                                    children: \"Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"grid gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"name\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Full name\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"email\",\n                                        type: \"email\",\n                                        required: true,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Work email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        name: \"company\",\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        name: \"message\",\n                                        rows: 4,\n                                        className: \"rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\",\n                                        placeholder: \"Tell us about your AI/cloud setup\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: `inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white transition-opacity ${isSubmitting ? \"opacity-50 cursor-not-allowed\" : \"hover:opacity-95\"}`,\n                                        children: isSubmitting ? \"Sending...\" : \"Request Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-xs text-zinc-500\",\n                                        children: \"We'll reply within 1 business day.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                id: \"ld-org\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"Organization\",\n                        \"name\": \"CloudNextAI\",\n                        \"url\": \"https://www.cloudnext.ai\",\n                        \"logo\": \"/favicon.svg\",\n                        \"sameAs\": [\n                            \"https://www.linkedin.com/company/cloudnextai\"\n                        ]\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                id: \"ld-product\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify({\n                        \"@context\": \"https://schema.org\",\n                        \"@type\": \"SoftwareApplication\",\n                        \"name\": \"CloudNextAI\",\n                        \"applicationCategory\": \"SecurityApplication\",\n                        \"operatingSystem\": \"Cloud\",\n                        \"offers\": {\n                            \"@type\": \"Offer\",\n                            \"price\": \"499\",\n                            \"priceCurrency\": \"USD\"\n                        }\n                    })\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n                lineNumber: 247,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDbUQ7QUFDZDtBQUNrQjtBQUNkO0FBQ0E7QUFDZTtBQUMrSDtBQUNySTtBQUNqQjtBQUtqQyxNQUFNdUIsWUFBWSxDQUFDLEVBQUVDLFlBQVksRUFBRSxFQUFFQyxRQUFRLEVBQW1ELGlCQUM5Riw4REFBQ0M7UUFBSUYsV0FBVyxDQUFDLDhDQUE4QyxFQUFFQSxVQUFVLENBQUM7a0JBQUdDOzs7Ozs7QUFFakYsTUFBTUUsVUFBVSxDQUFDLEVBQUVDLEVBQUUsRUFBRUosWUFBWSxFQUFFLEVBQUVDLFFBQVEsRUFBZ0UsaUJBQzdHLDhEQUFDSTtRQUFRRCxJQUFJQTtRQUFJSixXQUFXLENBQUMsZUFBZSxFQUFFQSxVQUFVLENBQUM7a0JBQUdDOzs7Ozs7QUFFOUQsTUFBTUssY0FBYztJQUFFQyxRQUFRO1FBQUVDLFNBQVM7UUFBR0MsR0FBRztJQUFFO0lBQUdDLE1BQU07UUFBRUYsU0FBUztRQUFHQyxHQUFHO1FBQUdFLFlBQVk7WUFBRUMsVUFBVTtRQUFJO0lBQUU7QUFBRTtBQUM5RyxNQUFNQyxNQUFNLENBQUMsRUFBRVosUUFBUSxFQUFpQyxpQkFBTSw4REFBQ2E7UUFBS2QsV0FBVTtrQkFBd0lDOzs7Ozs7QUFDdE4sTUFBTWMsT0FBTyxDQUFDLEVBQUVkLFFBQVEsRUFBaUMsaUJBQU0sOERBQUNhO1FBQUtkLFdBQVU7a0JBQW1IQzs7Ozs7O0FBQ2xNLE1BQU1lLFFBQVEsQ0FBQyxFQUFFZixRQUFRLEVBQWlDLGlCQUFNLDhEQUFDYTtRQUFLZCxXQUFVO2tCQUFnSEM7Ozs7OztBQUNoTSxNQUFNZ0IsT0FBTyxDQUFDLEVBQUVoQixRQUFRLEVBQUVELFlBQVksRUFBRSxFQUFtRCxpQkFBTSw4REFBQ0U7UUFBSUYsV0FBVyxDQUFDLDhHQUE4RyxFQUFFQSxVQUFVLENBQUM7a0JBQUdDOzs7Ozs7QUFDaFAsTUFBTWlCLFlBQVksQ0FBQyxFQUFFakIsUUFBUSxFQUFFa0IsSUFBSSxFQUF5RCxpQkFBTSw4REFBQ2pCO1FBQUlGLFdBQVU7O1lBQWlFbUI7MEJBQUssOERBQUNMOzBCQUFNYjs7Ozs7Ozs7Ozs7O0FBQzlMLE1BQU1tQixXQUFXLENBQUMsRUFBRW5CLFFBQVEsRUFBaUMsaUJBQU0sOERBQUNvQjtRQUFHckIsV0FBVTs7MEJBQStDLDhEQUFDUiw0TEFBWUE7Z0JBQUNRLFdBQVU7Ozs7OztZQUE2QkM7Ozs7Ozs7QUFDckwsTUFBTXFCLFdBQVcsQ0FBQyxFQUFFckIsUUFBUSxFQUFpQyxpQkFBTSw4REFBQ0M7UUFBSUYsV0FBVTtrQkFBc0RDOzs7Ozs7QUFFeEkscUNBQXFDO0FBQ3JDLE1BQU1zQixTQUFTLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEVBQUVDLFNBQVMsRUFBeUU7SUFDakgsTUFBTUMsWUFBWUgsU0FBUztJQUUzQjlDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWtELFFBQVFDLFdBQVc7WUFDdkJIO1FBQ0YsR0FBRyxPQUFPLCtCQUErQjtRQUV6QyxPQUFPLElBQU1JLGFBQWFGO0lBQzVCLEdBQUc7UUFBQ0Y7S0FBVTtJQUVkLHFCQUNFLDhEQUFDM0MsaURBQU1BLENBQUNtQixHQUFHO1FBQ1Q2QixTQUFTO1lBQUV2QixTQUFTO1lBQUdDLEdBQUcsQ0FBQztZQUFJdUIsT0FBTztRQUFLO1FBQzNDQyxTQUFTO1lBQUV6QixTQUFTO1lBQUdDLEdBQUc7WUFBR3VCLE9BQU87UUFBRTtRQUN0Q0UsTUFBTTtZQUFFMUIsU0FBUztZQUFHQyxHQUFHLENBQUM7WUFBSXVCLE9BQU87UUFBSztRQUN4Q3JCLFlBQVk7WUFBRUMsVUFBVTtRQUFJO1FBQzVCWixXQUFXLENBQUMsMElBQTBJLEVBQ3BKMkIsWUFDSSw2REFDQSwrQ0FDTCxDQUFDO2tCQUVGLDRFQUFDekI7WUFBSUYsV0FBVTs7OEJBQ2IsOERBQUNFO29CQUFJRixXQUFVOzt3QkFDWjJCLDBCQUNDLDhEQUFDbkMsNExBQVlBOzRCQUFDUSxXQUFVOzs7OztzREFFeEIsOERBQUNKLDZMQUFDQTs0QkFBQ0ksV0FBVTs7Ozs7O3NDQUVmLDhEQUFDYzs0QkFBS2QsV0FBVTtzQ0FBZ0N5Qjs7Ozs7Ozs7Ozs7OzhCQUVsRCw4REFBQ1U7b0JBQ0NDLFNBQVNWO29CQUNUMUIsV0FBVTtvQkFDVnFDLGNBQVc7OEJBRVgsNEVBQUN6Qyw2TEFBQ0E7d0JBQUNJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLdkI7QUFFZSxTQUFTc0M7SUFDdEIsTUFBTUMsZUFBZTFDLGdFQUFlQTtJQUNwQyxNQUFNMkMsVUFBVUQsYUFBYUUsR0FBRyxDQUFDO0lBRWpDLDBDQUEwQztJQUMxQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2xFLCtDQUFRQSxDQUF3RDtJQUM1RixNQUFNLENBQUNtRSxjQUFjQyxnQkFBZ0IsR0FBR3BFLCtDQUFRQSxDQUFDO0lBRWpELHdEQUF3RDtJQUN4REMsZ0RBQVNBLENBQUM7UUFDUixJQUFJOEQsWUFBWSxLQUFLO1lBQ25CRyxVQUFVO2dCQUFFbkIsTUFBTTtnQkFBV0MsU0FBUztZQUF3RDtRQUNoRyxPQUFPLElBQUllLFlBQVksS0FBSztZQUMxQkcsVUFBVTtnQkFBRW5CLE1BQU07Z0JBQVNDLFNBQVM7WUFBaUQ7UUFDdkY7SUFDRixHQUFHO1FBQUNlO0tBQVE7SUFFWixnREFBZ0Q7SUFDaEQsTUFBTU0sZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUNoQkgsZ0JBQWdCO1FBQ2hCRixVQUFVLE9BQU8sNEJBQTRCO1FBRTdDLE1BQU1NLFdBQVcsSUFBSUMsU0FBU0gsRUFBRUksYUFBYTtRQUU3QyxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGdCQUFnQjtnQkFDM0NDLFFBQVE7Z0JBQ1JDLE1BQU1OO1lBQ1I7WUFFQSxJQUFJRyxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2ZiLFVBQVU7b0JBQUVuQixNQUFNO29CQUFXQyxTQUFTO2dCQUF3RDtnQkFDOUYsYUFBYTtnQkFDWnNCLEVBQUVVLE1BQU0sQ0FBcUJDLEtBQUs7WUFDckMsT0FBTztnQkFDTGYsVUFBVTtvQkFBRW5CLE1BQU07b0JBQVNDLFNBQVM7Z0JBQWlEO1lBQ3ZGO1FBQ0YsRUFBRSxPQUFPa0MsT0FBTztZQUNkaEIsVUFBVTtnQkFBRW5CLE1BQU07Z0JBQVNDLFNBQVM7WUFBNkQ7UUFDbkcsU0FBVTtZQUNSb0IsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNZSxnQkFBZ0I7UUFDcEJqQixVQUFVO0lBQ1o7SUFFQSxxQkFDRSw4REFBQ2tCO1FBQUt6RCxJQUFHOzswQkFFUCw4REFBQ3BCLDJEQUFlQTswQkFDYjBELHdCQUNDLDhEQUFDbkI7b0JBQ0NDLE1BQU1rQixPQUFPbEIsSUFBSTtvQkFDakJDLFNBQVNpQixPQUFPakIsT0FBTztvQkFDdkJDLFdBQVdrQzs7Ozs7Ozs7Ozs7MEJBS2pCLDhEQUFDL0UsMERBQU1BOzs7OzswQkFDUCw4REFBQ0Ysd0RBQUlBOzs7OzswQkFDTCw4REFBQ0MsaUVBQWFBOzs7OzswQkFDZCw4REFBQ3NCO2dCQUFJRixXQUFVOzBCQUNiLDRFQUFDRztvQkFBUUgsV0FBVTs4QkFDakIsNEVBQUNEO2tDQUNDLDRFQUFDRzs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNqQixpREFBTUEsQ0FBQ21CLEdBQUc7b0NBQUM2QixTQUFTO3dDQUFFdkIsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRztvQ0FBR3dCLFNBQVM7d0NBQUV6QixTQUFTO3dDQUFHQyxHQUFHO29DQUFFO29DQUFHRSxZQUFZO3dDQUFFQyxVQUFVO29DQUFJOztzREFDckcsOERBQUNDO3NEQUFJOzs7Ozs7c0RBQ0wsOERBQUNpRDs0Q0FBRzlELFdBQVU7c0RBQStEOzs7Ozs7c0RBQzdFLDhEQUFDK0Q7NENBQUUvRCxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMxQyw4REFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDZ0U7b0RBQUVDLE1BQUs7b0RBQVdqRSxXQUFVOzt3REFBbUg7c0VBQStCLDhEQUFDUCw2TEFBVUE7NERBQUNPLFdBQVU7Ozs7Ozs7Ozs7Ozs4REFDck0sOERBQUNnRTtvREFBRUMsTUFBSztvREFBV2pFLFdBQVU7OERBQXlIOzs7Ozs7OERBQ3RKLDhEQUFDRTtvREFBSUYsV0FBVTs7c0VBQ2IsOERBQUNlOzs4RUFBSyw4REFBQ3pCLDZMQUFXQTtvRUFBQ1UsV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7OztzRUFDN0MsOERBQUNlOzs4RUFBSyw4REFBQzNCLDZMQUFLQTtvRUFBQ1ksV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7OztzRUFDdkMsOERBQUNlOzs4RUFBSyw4REFBQ3JCLDZMQUFjQTtvRUFBQ00sV0FBVTs7Ozs7O2dFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJdEQsOERBQUNqQixpREFBTUEsQ0FBQ21CLEdBQUc7b0NBQUM2QixTQUFTO3dDQUFFdkIsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRztvQ0FBR3dCLFNBQVM7d0NBQUV6QixTQUFTO3dDQUFHQyxHQUFHO29DQUFFO29DQUFHRSxZQUFZO3dDQUFFQyxVQUFVO3dDQUFLc0QsT0FBTztvQ0FBSTs4Q0FDakgsNEVBQUNoRTt3Q0FBSUYsV0FBVTtrREFDYiw0RUFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDaUI7O3NFQUFLLDhEQUFDQzs0REFBVUMsb0JBQU0sOERBQUNsQyw2TEFBV0E7Z0VBQUNlLFdBQVU7Ozs7OztzRUFBK0I7Ozs7OztzRUFBNkIsOERBQUNtRTs0REFBR25FLFdBQVU7OzhFQUEwQiw4REFBQ29COzhFQUFTOzs7Ozs7OEVBQTJELDhEQUFDQTs4RUFBUzs7Ozs7OzhFQUF3Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFDblIsOERBQUNIOztzRUFBSyw4REFBQ0M7NERBQVVDLG9CQUFNLDhEQUFDakMsNkxBQVFBO2dFQUFDYyxXQUFVOzs7Ozs7c0VBQTRCOzs7Ozs7c0VBQThCLDhEQUFDbUU7NERBQUduRSxXQUFVOzs4RUFBMEIsOERBQUNvQjs4RUFBUzs7Ozs7OzhFQUE0Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs4RUFBd0MsOERBQUNBOzhFQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBQy9QLDhEQUFDSDtvREFBS2pCLFdBQVU7O3NFQUFhLDhEQUFDa0I7NERBQVVDLG9CQUFNLDhEQUFDaEMsNkxBQVNBO2dFQUFDYSxXQUFVOzs7Ozs7c0VBQStCOzs7Ozs7c0VBQThCLDhEQUFDbUU7NERBQUduRSxXQUFVOzs4RUFBNEIsOERBQUNvQjs4RUFBUzs7Ozs7OzhFQUF1Qyw4REFBQ0E7OEVBQVM7Ozs7Ozs4RUFBNEMsOERBQUNBOzhFQUFTOzs7Ozs7OEVBQThDLDhEQUFDQTs4RUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFValcsOERBQUNqQjtnQkFBUUMsSUFBRzswQkFDViw0RUFBQ0w7O3NDQUNDLDhEQUFDRzs0QkFBSUYsV0FBVTs7OENBQWdDLDhEQUFDb0U7b0NBQUdwRSxXQUFVOzhDQUFnQzs7Ozs7OzhDQUFnQiw4REFBQytEO29DQUFFL0QsV0FBVTs4Q0FBcUI7Ozs7Ozs7Ozs7OztzQ0FDL0ksOERBQUNFOzRCQUFJRixXQUFVO3NDQUNaO2dDQUFDO29DQUFFcUUsT0FBTztvQ0FBMEJsRCxvQkFBTSw4REFBQ2pDLDZMQUFRQTt3Q0FBQ2MsV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBd0JsRCxvQkFBTSw4REFBQzlCLDZMQUFLQTt3Q0FBQ1csV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBNEJsRCxvQkFBTSw4REFBQ3hCLDZMQUFLQTt3Q0FBQ0ssV0FBVTs7Ozs7O2dDQUFhO2dDQUFFO29DQUFFcUUsT0FBTztvQ0FBNkJsRCxvQkFBTSw4REFBQzVCLDZMQUFVQTt3Q0FBQ1MsV0FBVTs7Ozs7O2dDQUFhOzZCQUFFLENBQUNzRSxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3hULDhEQUFDekYsaURBQU1BLENBQUNtQixHQUFHO29DQUFTNkIsU0FBUzt3Q0FBQ3ZCLFNBQVE7d0NBQUVDLEdBQUU7b0NBQUM7b0NBQUdnRSxhQUFhO3dDQUFDakUsU0FBUTt3Q0FBRUMsR0FBRTtvQ0FBQztvQ0FBR2lFLFVBQVU7d0NBQUVDLE1BQU07b0NBQUs7b0NBQUdoRSxZQUFZO3dDQUFDQyxVQUFTO29DQUFHO29DQUFHWixXQUFVOztzREFDMUksOERBQUNFOzRDQUFJRixXQUFVOztnREFBc0N1RSxFQUFFcEQsSUFBSTs4REFBQyw4REFBQ3lEO29EQUFHNUUsV0FBVTs4REFBeUJ1RSxFQUFFRixLQUFLOzs7Ozs7Ozs7Ozs7c0RBQzFHLDhEQUFDTjs0Q0FBRS9ELFdBQVU7c0RBQTZCOzs7Ozs7O21DQUYzQndFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU3pCLDhEQUFDckU7Z0JBQVFDLElBQUc7Z0JBQVVKLFdBQVU7MEJBQzlCLDRFQUFDRDs7c0NBQ0MsOERBQUNHOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ29FO29DQUFHcEUsV0FBVTs4Q0FBZ0M7Ozs7Ozs4Q0FDOUMsOERBQUMrRDtvQ0FBRS9ELFdBQVU7OENBQXFCOzs7Ozs7Ozs7Ozs7c0NBRXBDLDhEQUFDRTs0QkFBSUYsV0FBVTtzQ0FDYiw0RUFBQzZFO2dDQUFLQyxVQUFVaEM7Z0NBQWM5QyxXQUFVOztrREFDdEMsOERBQUMrRTt3Q0FDQ0MsTUFBSzt3Q0FDTEMsUUFBUTt3Q0FDUmpGLFdBQVU7d0NBQ1ZrRixhQUFZOzs7Ozs7a0RBRWQsOERBQUNIO3dDQUNDQyxNQUFLO3dDQUNMeEQsTUFBSzt3Q0FDTHlELFFBQVE7d0NBQ1JqRixXQUFVO3dDQUNWa0YsYUFBWTs7Ozs7O2tEQUVkLDhEQUFDSDt3Q0FDQ0MsTUFBSzt3Q0FDTGhGLFdBQVU7d0NBQ1ZrRixhQUFZOzs7Ozs7a0RBRWQsOERBQUNDO3dDQUNDSCxNQUFLO3dDQUNMSSxNQUFNO3dDQUNOcEYsV0FBVTt3Q0FDVmtGLGFBQVk7Ozs7OztrREFFZCw4REFBQy9DO3dDQUNDWCxNQUFLO3dDQUNMNkQsVUFBVXpDO3dDQUNWNUMsV0FBVyxDQUFDLDZKQUE2SixFQUN2SzRDLGVBQWUsa0NBQWtDLG1CQUNsRCxDQUFDO2tEQUVEQSxlQUFlLGVBQWU7Ozs7OztrREFFakMsOERBQUNtQjt3Q0FBRS9ELFdBQVU7a0RBQW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU16RCw4REFBQ2xCLDBEQUFNQTs7Ozs7MEJBQ1AsOERBQUNnQixtREFBTUE7Z0JBQUNNLElBQUc7Z0JBQVNvQixNQUFLO2dCQUM3QjhELHlCQUF5QjtvQkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQzt3QkFDckIsWUFBWTt3QkFDWixTQUFTO3dCQUNULFFBQVE7d0JBQ1IsT0FBTzt3QkFDUCxRQUFRO3dCQUNSLFVBQVU7NEJBQUM7eUJBQStDO29CQUM1RDtnQkFDRjs7Ozs7OzBCQUVGLDhEQUFDM0YsbURBQU1BO2dCQUFDTSxJQUFHO2dCQUFhb0IsTUFBSztnQkFDM0I4RCx5QkFBeUI7b0JBQ3ZCQyxRQUFRQyxLQUFLQyxTQUFTLENBQUM7d0JBQ3JCLFlBQVk7d0JBQ1osU0FBUzt3QkFDVCxRQUFRO3dCQUNSLHVCQUF1Qjt3QkFDdkIsbUJBQW1CO3dCQUNuQixVQUFVOzRCQUNSLFNBQVM7NEJBQ1QsU0FBUzs0QkFDVCxpQkFBaUI7d0JBQ25CO29CQUNGO2dCQUNGOzs7Ozs7Ozs7Ozs7QUFLRiIsInNvdXJjZXMiOlsid2VicGFjazovL2Nsb3VkbmV4dGFpLXNpdGUvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgSGVybyBmcm9tIFwiQC9jb21wb25lbnRzL0hlcm9cIjtcbmltcG9ydCBCZW5lZml0c1N0cmlwIGZyb20gXCJAL2NvbXBvbmVudHMvQmVuZWZpdHNTdHJpcFwiO1xuaW1wb3J0IE5hdmJhciBmcm9tIFwiQC9jb21wb25lbnRzL05hdmJhclwiO1xuaW1wb3J0IEZvb3RlciBmcm9tIFwiQC9jb21wb25lbnRzL0Zvb3RlclwiO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuaW1wb3J0IHsgU2hpZWxkQ2hlY2ssIEtleVJvdW5kLCBGaWxlQ2hlY2ssIFdhbmQyLCBHYXVnZSwgTG9ja0tleWhvbGUsIFNjcm9sbFRleHQsIENoZWNrQ2lyY2xlMiwgQXJyb3dSaWdodCwgQnVpbGRpbmcyLCBCb3hlcywgV29ya2Zsb3csIENsaXBib2FyZENoZWNrLCBSYWRhciwgWCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCBTY3JpcHQgZnJvbSBcIm5leHQvc2NyaXB0XCI7XG5cblxuXG5cbmNvbnN0IENvbnRhaW5lciA9ICh7IGNsYXNzTmFtZSA9IFwiXCIsIGNoaWxkcmVuIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+KSA9PiAoXG4gIDxkaXYgY2xhc3NOYW1lPXtgbXgtYXV0byB3LWZ1bGwgbWF4LXctN3hsIHB4LTQgc206cHgtNiBsZzpweC04ICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L2Rpdj5cbik7XG5jb25zdCBTZWN0aW9uID0gKHsgaWQsIGNsYXNzTmFtZSA9IFwiXCIsIGNoaWxkcmVuIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgaWQ/OiBzdHJpbmc7IGNsYXNzTmFtZT86IHN0cmluZyB9PikgPT4gKFxuICA8c2VjdGlvbiBpZD17aWR9IGNsYXNzTmFtZT17YHB5LTE2IHNtOnB5LTI0ICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L3NlY3Rpb24+XG4pO1xuY29uc3QgZmVhdHVyZUZhZGUgPSB7IGhpZGRlbjogeyBvcGFjaXR5OiAwLCB5OiA4IH0sIHNob3c6IHsgb3BhY2l0eTogMSwgeTogMCwgdHJhbnNpdGlvbjogeyBkdXJhdGlvbjogMC40IH0gfSB9O1xuY29uc3QgVGFnID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItd2hpdGUvMjAgYmctd2hpdGUvNSBweC0zIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzkwIGJhY2tkcm9wLWJsdXItc21cIj57Y2hpbGRyZW59PC9zcGFuPik7XG5jb25zdCBQaWxsID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC1mdWxsIGJnLXppbmMtOTAwLzgwIHB4LTMgcHktMSB0ZXh0LXhzIHRleHQtemluYy0yMDAgcmluZy0xIHJpbmctd2hpdGUvMTBcIj57Y2hpbGRyZW59PC9zcGFuPik7XG5jb25zdCBCYWRnZSA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPHNwYW4gY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGJnLWVtZXJhbGQtNjAwLzE1IHB4LTIuNSBweS0wLjUgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWVtZXJhbGQtNDAwIHJpbmctMSByaW5nLWVtZXJhbGQtNTAwLzMwXCI+e2NoaWxkcmVufTwvc3Bhbj4pO1xuY29uc3QgQ2FyZCA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSBcIlwiIH06IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+KSA9PiAoPGRpdiBjbGFzc05hbWU9e2Byb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTAwLzYwIHAtNiBzaGFkb3ctWzBfMF8wXzFweF9yZ2JhKDI1NSwyNTUsMjU1LDAuMDQpXSBiYWNrZHJvcC1ibHVyICR7Y2xhc3NOYW1lfWB9PntjaGlsZHJlbn08L2Rpdj4pO1xuY29uc3QgQ2FyZFRpdGxlID0gKHsgY2hpbGRyZW4sIGljb24gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlOyBpY29uPzogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8ZGl2IGNsYXNzTmFtZT1cIm1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57aWNvbn08c3Bhbj57Y2hpbGRyZW59PC9zcGFuPjwvZGl2Pik7XG5jb25zdCBDYXJkSXRlbSA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiAoPGxpIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTIgdGV4dC1zbSB0ZXh0LXppbmMtMzAwXCI+PENoZWNrQ2lyY2xlMiBjbGFzc05hbWU9XCJtdC0wLjUgaC00IHctNCBzaHJpbmstMFwiIC8+e2NoaWxkcmVufTwvbGk+KTtcbmNvbnN0IFByaWNlUm93ID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pID0+ICg8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIGdhcC0xIHRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+e2NoaWxkcmVufTwvZGl2Pik7XG5cbi8vIEJhbm5lciBjb21wb25lbnQgd2l0aCBhdXRvLWRpc21pc3NcbmNvbnN0IEJhbm5lciA9ICh7IHR5cGUsIG1lc3NhZ2UsIG9uRGlzbWlzcyB9OiB7IHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcic7IG1lc3NhZ2U6IHN0cmluZzsgb25EaXNtaXNzOiAoKSA9PiB2b2lkIH0pID0+IHtcbiAgY29uc3QgaXNTdWNjZXNzID0gdHlwZSA9PT0gJ3N1Y2Nlc3MnO1xuICBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgb25EaXNtaXNzKCk7XG4gICAgfSwgNTAwMCk7IC8vIEF1dG8tZGlzbWlzcyBhZnRlciA1IHNlY29uZHNcbiAgICBcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgfSwgW29uRGlzbWlzc10pO1xuXG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5kaXZcbiAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogLTUwLCBzY2FsZTogMC45NSB9fVxuICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwLCBzY2FsZTogMSB9fVxuICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiAtNTAsIHNjYWxlOiAwLjk1IH19XG4gICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgIGNsYXNzTmFtZT17YGZpeGVkIHRvcC00IGxlZnQtMS8yIHotNTAgdy1bY2FsYygxMDB2dy0ycmVtKV0gbWF4LXctbWQgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgcm91bmRlZC14bCBib3JkZXIgcHgtNCBweS0zIHNoYWRvdy1sZyBiYWNrZHJvcC1ibHVyLXNtICR7XG4gICAgICAgIGlzU3VjY2Vzc1xuICAgICAgICAgID8gJ2JvcmRlci1lbWVyYWxkLTUwMC8zMCBiZy1lbWVyYWxkLTUwMC8xMCB0ZXh0LWVtZXJhbGQtMzAwJ1xuICAgICAgICAgIDogJ2JvcmRlci1yZWQtNTAwLzMwIGJnLXJlZC01MDAvMTAgdGV4dC1yZWQtMzAwJ1xuICAgICAgfWB9XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTNcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgIHtpc1N1Y2Nlc3MgPyAoXG4gICAgICAgICAgICA8Q2hlY2tDaXJjbGUyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1lbWVyYWxkLTQwMCBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC00MDAgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRydW5jYXRlXCI+e21lc3NhZ2V9PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e29uRGlzbWlzc31cbiAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGwgcC0xIGhvdmVyOmJnLXdoaXRlLzEwIHRyYW5zaXRpb24tY29sb3JzIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgIGFyaWEtbGFiZWw9XCJEaXNtaXNzIG5vdGlmaWNhdGlvblwiXG4gICAgICAgID5cbiAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbiAgY29uc3QgdXJsU2VudCA9IHNlYXJjaFBhcmFtcy5nZXQoXCJzZW50XCIpO1xuICBcbiAgLy8gU3RhdGUgZm9yIGNsaWVudC1zaWRlIGJhbm5lciBtYW5hZ2VtZW50XG4gIGNvbnN0IFtiYW5uZXIsIHNldEJhbm5lcl0gPSB1c2VTdGF0ZTx7IHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcic7IG1lc3NhZ2U6IHN0cmluZyB9IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gSGFuZGxlIFVSTC1iYXNlZCBiYW5uZXJzIChmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eSlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXJsU2VudCA9PT0gXCIxXCIpIHtcbiAgICAgIHNldEJhbm5lcih7IHR5cGU6ICdzdWNjZXNzJywgbWVzc2FnZTogJ1RoYW5rcyEgV2VcXCdsbCBnZXQgYmFjayB0byB5b3Ugd2l0aGluIDEgYnVzaW5lc3MgZGF5LicgfSk7XG4gICAgfSBlbHNlIGlmICh1cmxTZW50ID09PSBcIjBcIikge1xuICAgICAgc2V0QmFubmVyKHsgdHlwZTogJ2Vycm9yJywgbWVzc2FnZTogJ1NvcnJ5LCBzb21ldGhpbmcgd2VudCB3cm9uZy4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH0pO1xuICAgIH1cbiAgfSwgW3VybFNlbnRdKTtcblxuICAvLyBIYW5kbGUgZm9ybSBzdWJtaXNzaW9uIHdpdGggY2xpZW50LXNpZGUgc3RhdGVcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudDxIVE1MRm9ybUVsZW1lbnQ+KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldElzU3VibWl0dGluZyh0cnVlKTtcbiAgICBzZXRCYW5uZXIobnVsbCk7IC8vIENsZWFyIGFueSBleGlzdGluZyBiYW5uZXJcblxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKGUuY3VycmVudFRhcmdldCk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY29udGFjdCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGJvZHk6IGZvcm1EYXRhLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBzZXRCYW5uZXIoeyB0eXBlOiAnc3VjY2VzcycsIG1lc3NhZ2U6ICdUaGFua3MhIFdlXFwnbGwgZ2V0IGJhY2sgdG8geW91IHdpdGhpbiAxIGJ1c2luZXNzIGRheS4nIH0pO1xuICAgICAgICAvLyBSZXNldCBmb3JtXG4gICAgICAgIChlLnRhcmdldCBhcyBIVE1MRm9ybUVsZW1lbnQpLnJlc2V0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRCYW5uZXIoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiAnU29ycnksIHNvbWV0aGluZyB3ZW50IHdyb25nLiBQbGVhc2UgdHJ5IGFnYWluLicgfSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldEJhbm5lcih7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6ICdOZXR3b3JrIGVycm9yLiBQbGVhc2UgY2hlY2sgeW91ciBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uJyB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTdWJtaXR0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZGlzbWlzc0Jhbm5lciA9ICgpID0+IHtcbiAgICBzZXRCYW5uZXIobnVsbCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8bWFpbiBpZD1cIm1haW5cIj5cbiAgICAgIHsvKiBGaXhlZCBwb3NpdGlvbiBiYW5uZXIgYXQgdG9wICovfVxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge2Jhbm5lciAmJiAoXG4gICAgICAgICAgPEJhbm5lciBcbiAgICAgICAgICAgIHR5cGU9e2Jhbm5lci50eXBlfSBcbiAgICAgICAgICAgIG1lc3NhZ2U9e2Jhbm5lci5tZXNzYWdlfSBcbiAgICAgICAgICAgIG9uRGlzbWlzcz17ZGlzbWlzc0Jhbm5lcn0gXG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgXG4gICAgICA8TmF2YmFyIC8+XG4gICAgICA8SGVybyAvPlxuICAgICAgPEJlbmVmaXRzU3RyaXAgLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuIGJnLVtyYWRpYWwtZ3JhZGllbnQoNjByZW1fNjByZW1fYXRfNTAlXy0yMHJlbSxyZ2JhKDE2LDE4NSwxMjksMC4yNSksdHJhbnNwYXJlbnQpLHJhZGlhbC1ncmFkaWVudCg2MHJlbV82MHJlbV9hdF8tMTAlXy0xMCUscmdiYSgzNCwyMTEsMjM4LDAuMjUpLHRyYW5zcGFyZW50KV1cIj5cbiAgICAgICAgPFNlY3Rpb24gY2xhc3NOYW1lPVwicGItMjAgcHQtMjRcIj5cbiAgICAgICAgICA8Q29udGFpbmVyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGl0ZW1zLWNlbnRlciBnYXAtMTAgbWQ6Z3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXYgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAxMiB9fSBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX0gdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19PlxuICAgICAgICAgICAgICAgIDxUYWc+QUkgKyBDbG91ZCBTZWN1cml0eSBDb3BpbG90IGZvciBTTUJzPC9UYWc+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cIm10LTQgdGV4dC00eGwgZm9udC1ib2xkIGxlYWRpbmctdGlnaHQgdGV4dC13aGl0ZSBzbTp0ZXh0LTV4bFwiPlNlY3VyZSB5b3VyIEFJIGFnZW50cyAmIGNsb3VkIOKAlCB3aXRob3V0IGhpcmluZyBhIHNlY3VyaXR5IHRlYW08L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1sZyB0ZXh0LXppbmMtMzAwXCI+Q2xvdWROZXh0QUkgcHJvdGVjdHMgeW91ciBBSSBhbmQgR0NQIHdvcmtsb2FkcyBmcm9tIGRhdGEgbGVha3MsIElBTSBtaXNjb25maWdzLCBhbmQgY29tcGxpYW5jZSBmYWlsdXJlcy4gS2V5bGVzcyBieSBkZWZhdWx0LiBTTUItZnJpZW5kbHkuIFByb2R1Y3Rpb24gcmVhZHkuPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBmbGV4IGZsZXgtd3JhcCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjY29udGFjdFwiIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiByb3VuZGVkLXhsIGJnLXdoaXRlIHB4LTUgcHktMi41IGZvbnQtc2VtaWJvbGQgdGV4dC1ibGFjayBzaGFkb3cgaG92ZXI6YmctemluYy0xMDBcIj5GcmVlIDMwLW1pbiBzZWN1cml0eSBhc3Nlc3NtZW50PEFycm93UmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+PC9hPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIi9wcm9kdWN0XCIgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBweC01IHB5LTIuNSBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgaG92ZXI6Ymctd2hpdGUvNVwiPlNlZSBob3cgaXQgd29ya3M8L2E+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC14cyB0ZXh0LXppbmMtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxQaWxsPjxMb2NrS2V5aG9sZSBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNVwiIC8+IEtleWxlc3MgSUFNIChXSUYpPC9QaWxsPlxuICAgICAgICAgICAgICAgICAgICA8UGlsbD48V2FuZDIgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjVcIiAvPiBQcm9tcHQgRmlyZXdhbGw8L1BpbGw+XG4gICAgICAgICAgICAgICAgICAgIDxQaWxsPjxDbGlwYm9hcmRDaGVjayBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNVwiIC8+IENvbXBsaWFuY2UgUGFja3M8L1BpbGw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICA8bW90aW9uLmRpdiBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDEyIH19IGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fSB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjEgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSByb3VuZGVkLTN4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLXppbmMtOTUwLzYwIHAtNCBzaGFkb3ctMnhsXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+PENhcmRUaXRsZSBpY29uPXs8U2hpZWxkQ2hlY2sgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWVtZXJhbGQtNDAwXCIgLz59PkFJIEFnZW50IFNlY3VyaXR5PC9DYXJkVGl0bGU+PHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXppbmMtMzAwXCI+PENhcmRJdGVtPlByb21wdCBmaXJld2FsbCBibG9ja3MgamFpbGJyZWFrcyAmIGV4ZmlsdHJhdGlvbjwvQ2FyZEl0ZW0+PENhcmRJdGVtPlBJSS9QSEkgcmVkYWN0aW9uIHZpYSBHQ1AgRExQPC9DYXJkSXRlbT48Q2FyZEl0ZW0+VG9vbCBhbGxvd2xpc3RpbmcgZm9yIEFQSXMvREJzPC9DYXJkSXRlbT48L3VsPjwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+PENhcmRUaXRsZSBpY29uPXs8S2V5Um91bmQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWN5YW4tNDAwXCIgLz59PkdDUCBJQU0gR3VhcmRyYWlsczwvQ2FyZFRpdGxlPjx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC16aW5jLTMwMFwiPjxDYXJkSXRlbT5Xb3JrbG9hZCBJZGVudGl0eSwgbm8gc3RhdGljIGtleXM8L0NhcmRJdGVtPjxDYXJkSXRlbT5TaG9ydC1saXZlZCBwZXItYWN0aW9uIHRva2VuczwvQ2FyZEl0ZW0+PENhcmRJdGVtPkRvd25zY29waW5nIHZpYSBDcmVkZW50aWFsIEFjY2VzcyBCb3VuZGFyaWVzPC9DYXJkSXRlbT48L3VsPjwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPjxDYXJkVGl0bGUgaWNvbj17PEZpbGVDaGVjayBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZW1lcmFsZC00MDBcIiAvPn0+Q29tcGxpYW5jZSBDb3BpbG90PC9DYXJkVGl0bGU+PHVsIGNsYXNzTmFtZT1cImdyaWQgZ2FwLTIgc206Z3JpZC1jb2xzLTJcIj48Q2FyZEl0ZW0+SGFzaC1jaGFpbmVkIGV2aWRlbmNlIGxlZGdlcjwvQ2FyZEl0ZW0+PENhcmRJdGVtPkhJUEFBLCBTT0MgMiwgUElQRURBIDEtY2xpY2sgUERGczwvQ2FyZEl0ZW0+PENhcmRJdGVtPlBvbGljeSB2ZXJzaW9uaW5nICYgZHJpZnQgZGV0ZWN0aW9uPC9DYXJkSXRlbT48Q2FyZEl0ZW0+QXBwcm92YWwgd29ya2Zsb3dzPC9DYXJkSXRlbT48L3VsPjwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgICAgPC9TZWN0aW9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcm9ibGVtL1NvbHV0aW9uL0hvdy9Db21wbGlhbmNlL1ByaWNpbmcgZnJvbSBsYW5kaW5nICovfVxuICAgICAgPFNlY3Rpb24gaWQ9XCJwcm9ibGVtXCI+XG4gICAgICAgIDxDb250YWluZXI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTN4bCB0ZXh0LWNlbnRlclwiPjxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlRoZSBQcm9ibGVtPC9oMj48cCBjbGFzc05hbWU9XCJtdC0zIHRleHQtemluYy0zMDBcIj5TTUJzIHdhbnQgQUkgJiBjbG91ZCBzcGVlZCBidXQgY2FuJ3QgYWZmb3JkIHNlY3VyaXR5IHRlYW1zLiBSZXN1bHQ6IHN0YXRpYyBrZXlzIGluIHJlcG9zLCBvdmVyLWJyb2FkIElBTSwgcHJvbXB0IGluamVjdGlvbiByaXNrcywgYW5kIGF1ZGl0IGZhaWx1cmVzLjwvcD48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEwIGdyaWQgZ2FwLTQgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAgICAgIHtbeyB0aXRsZTogXCJTdGF0aWMga2V5cyBldmVyeXdoZXJlXCIsIGljb246IDxLZXlSb3VuZCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gfSx7IHRpdGxlOiBcIk92ZXItYnJvYWQgSUFNIHJvbGVzXCIsIGljb246IDxHYXVnZSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gfSx7IHRpdGxlOiBcIlByb21wdCBpbmplY3Rpb24gJiBleGZpbFwiLCBpY29uOiA8UmFkYXIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IH0seyB0aXRsZTogXCJDb21wbGlhbmNlIGV2aWRlbmNlIGNoYW9zXCIsIGljb246IDxTY3JvbGxUZXh0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPiB9XS5tYXAoKGYsIGkpID0+IChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXYga2V5PXtpfSBpbml0aWFsPXt7b3BhY2l0eTowLHk6OH19IHdoaWxlSW5WaWV3PXt7b3BhY2l0eToxLHk6MH19IHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX0gdHJhbnNpdGlvbj17e2R1cmF0aW9uOjAuNH19IGNsYXNzTmFtZT1cInJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctemluYy05MDAvNjAgcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXdoaXRlXCI+e2YuaWNvbn08aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+e2YudGl0bGV9PC9oMz48L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC16aW5jLTMwMFwiPldlIHJlcGxhY2UgYnJpdHRsZSBtYW51YWwgY29udHJvbHMgd2l0aCBvcGluaW9uYXRlZCBndWFyZHJhaWxzIGFuZCBhdXRvbWF0aW9uIGRlc2lnbmVkIGZvciBsZWFuIHRlYW1zLjwvcD5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ29udGFpbmVyPlxuICAgICAgPC9TZWN0aW9uPlxuXG4gICAgICA8U2VjdGlvbiBpZD1cImNvbnRhY3RcIiBjbGFzc05hbWU9XCJiZy16aW5jLTk1MC82MFwiPlxuICAgICAgICA8Q29udGFpbmVyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy0zeGwgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkJvb2sgYSBGcmVlIFNlY3VyaXR5IEFzc2Vzc21lbnQ8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMyB0ZXh0LXppbmMtMzAwXCI+R2V0IGEgcXVpY2sgZ2FwIHJlcG9ydCBvbiBJQU0sIHByb21wdHMsIGFuZCBjb21wbGlhbmNlIHJlYWRpbmVzcyBmb3IgeW91ciBHQ1AgcHJvamVjdHMuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtdC04IG1heC13LXhsIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctemluYy05MDAvNjAgcC02XCI+XG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxpbnB1dCBcbiAgICAgICAgICAgICAgICBuYW1lPVwibmFtZVwiIFxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGJnLWJsYWNrLzQwIHB4LTQgcHktMyB0ZXh0LXNtIHRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC16aW5jLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDBcIiBcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZ1bGwgbmFtZVwiIFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8aW5wdXQgXG4gICAgICAgICAgICAgICAgbmFtZT1cImVtYWlsXCIgXG4gICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCIgXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctYmxhY2svNDAgcHgtNCBweS0zIHRleHQtc20gdGV4dC13aGl0ZSBwbGFjZWhvbGRlcjp0ZXh0LXppbmMtNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1lbWVyYWxkLTUwMFwiIFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV29yayBlbWFpbFwiIFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8aW5wdXQgXG4gICAgICAgICAgICAgICAgbmFtZT1cImNvbXBhbnlcIiBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctYmxhY2svNDAgcHgtNCBweS0zIHRleHQtc20gdGV4dC13aGl0ZSBwbGFjZWhvbGRlcjp0ZXh0LXppbmMtNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1lbWVyYWxkLTUwMFwiIFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ29tcGFueVwiIFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8dGV4dGFyZWEgXG4gICAgICAgICAgICAgICAgbmFtZT1cIm1lc3NhZ2VcIiBcbiAgICAgICAgICAgICAgICByb3dzPXs0fSBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgYmctYmxhY2svNDAgcHgtNCBweS0zIHRleHQtc20gdGV4dC13aGl0ZSBwbGFjZWhvbGRlcjp0ZXh0LXppbmMtNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1lbWVyYWxkLTUwMFwiIFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVGVsbCB1cyBhYm91dCB5b3VyIEFJL2Nsb3VkIHNldHVwXCIgXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1yIGZyb20tZW1lcmFsZC01MDAgdG8tY3lhbi01MDAgcHgtNSBweS0zIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSB0cmFuc2l0aW9uLW9wYWNpdHkgJHtcbiAgICAgICAgICAgICAgICAgIGlzU3VibWl0dGluZyA/ICdvcGFjaXR5LTUwIGN1cnNvci1ub3QtYWxsb3dlZCcgOiAnaG92ZXI6b3BhY2l0eS05NSdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1N1Ym1pdHRpbmcgPyAnU2VuZGluZy4uLicgOiAnUmVxdWVzdCBBc3Nlc3NtZW50J31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQteHMgdGV4dC16aW5jLTUwMFwiPldlJ2xsIHJlcGx5IHdpdGhpbiAxIGJ1c2luZXNzIGRheS48L3A+XG4gICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ29udGFpbmVyPlxuICAgICAgPC9TZWN0aW9uPlxuXG4gICAgICA8Rm9vdGVyIC8+XG4gICAgICA8U2NyaXB0IGlkPVwibGQtb3JnXCIgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgXCJAY29udGV4dFwiOiBcImh0dHBzOi8vc2NoZW1hLm9yZ1wiLFxuICAgICAgXCJAdHlwZVwiOiBcIk9yZ2FuaXphdGlvblwiLFxuICAgICAgXCJuYW1lXCI6IFwiQ2xvdWROZXh0QUlcIixcbiAgICAgIFwidXJsXCI6IFwiaHR0cHM6Ly93d3cuY2xvdWRuZXh0LmFpXCIsXG4gICAgICBcImxvZ29cIjogXCIvZmF2aWNvbi5zdmdcIixcbiAgICAgIFwic2FtZUFzXCI6IFtcImh0dHBzOi8vd3d3LmxpbmtlZGluLmNvbS9jb21wYW55L2Nsb3VkbmV4dGFpXCJdXG4gICAgfSlcbiAgfX1cbi8+XG48U2NyaXB0IGlkPVwibGQtcHJvZHVjdFwiIHR5cGU9XCJhcHBsaWNhdGlvbi9sZCtqc29uXCJcbiAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgIFwiQGNvbnRleHRcIjogXCJodHRwczovL3NjaGVtYS5vcmdcIixcbiAgICAgIFwiQHR5cGVcIjogXCJTb2Z0d2FyZUFwcGxpY2F0aW9uXCIsXG4gICAgICBcIm5hbWVcIjogXCJDbG91ZE5leHRBSVwiLFxuICAgICAgXCJhcHBsaWNhdGlvbkNhdGVnb3J5XCI6IFwiU2VjdXJpdHlBcHBsaWNhdGlvblwiLFxuICAgICAgXCJvcGVyYXRpbmdTeXN0ZW1cIjogXCJDbG91ZFwiLFxuICAgICAgXCJvZmZlcnNcIjoge1xuICAgICAgICBcIkB0eXBlXCI6IFwiT2ZmZXJcIixcbiAgICAgICAgXCJwcmljZVwiOiBcIjQ5OVwiLFxuICAgICAgICBcInByaWNlQ3VycmVuY3lcIjogXCJVU0RcIlxuICAgICAgfVxuICAgIH0pXG4gIH19XG4vPlxuXG4gICAgPC9tYWluPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJIZXJvIiwiQmVuZWZpdHNTdHJpcCIsIk5hdmJhciIsIkZvb3RlciIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIlNoaWVsZENoZWNrIiwiS2V5Um91bmQiLCJGaWxlQ2hlY2siLCJXYW5kMiIsIkdhdWdlIiwiTG9ja0tleWhvbGUiLCJTY3JvbGxUZXh0IiwiQ2hlY2tDaXJjbGUyIiwiQXJyb3dSaWdodCIsIkNsaXBib2FyZENoZWNrIiwiUmFkYXIiLCJYIiwidXNlU2VhcmNoUGFyYW1zIiwiU2NyaXB0IiwiQ29udGFpbmVyIiwiY2xhc3NOYW1lIiwiY2hpbGRyZW4iLCJkaXYiLCJTZWN0aW9uIiwiaWQiLCJzZWN0aW9uIiwiZmVhdHVyZUZhZGUiLCJoaWRkZW4iLCJvcGFjaXR5IiwieSIsInNob3ciLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJUYWciLCJzcGFuIiwiUGlsbCIsIkJhZGdlIiwiQ2FyZCIsIkNhcmRUaXRsZSIsImljb24iLCJDYXJkSXRlbSIsImxpIiwiUHJpY2VSb3ciLCJCYW5uZXIiLCJ0eXBlIiwibWVzc2FnZSIsIm9uRGlzbWlzcyIsImlzU3VjY2VzcyIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImluaXRpYWwiLCJzY2FsZSIsImFuaW1hdGUiLCJleGl0IiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJQYWdlIiwic2VhcmNoUGFyYW1zIiwidXJsU2VudCIsImdldCIsImJhbm5lciIsInNldEJhbm5lciIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJjdXJyZW50VGFyZ2V0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImJvZHkiLCJvayIsInRhcmdldCIsInJlc2V0IiwiZXJyb3IiLCJkaXNtaXNzQmFubmVyIiwibWFpbiIsImgxIiwicCIsImEiLCJocmVmIiwiZGVsYXkiLCJ1bCIsImgyIiwidGl0bGUiLCJtYXAiLCJmIiwiaSIsIndoaWxlSW5WaWV3Iiwidmlld3BvcnQiLCJvbmNlIiwiaDMiLCJmb3JtIiwib25TdWJtaXQiLCJpbnB1dCIsIm5hbWUiLCJyZXF1aXJlZCIsInBsYWNlaG9sZGVyIiwidGV4dGFyZWEiLCJyb3dzIiwiZGlzYWJsZWQiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/BenefitsStrip.tsx":
/*!**************************************!*\
  !*** ./components/BenefitsStrip.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BenefitsStrip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,FileCheck2,KeyRound,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n// components/BenefitsStrip.tsx\n\n\nconst items = [\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Keyless by default\",\n        blurb: \"Short-lived tokens via Workload Identity Federation.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Least-privilege IAM\",\n        blurb: \"Downscoped access with continuous drift detection.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Audit & compliance\",\n        blurb: \"Exportable HIPAA • SOC 2 • PIPEDA evidence.\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_FileCheck2_KeyRound_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Production-ready\",\n        blurb: \"Signed builds, provenance, and OTel tracing.\"\n    }\n];\nfunction BenefitsStrip() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        \"aria-label\": \"Key benefits\",\n        className: \"border-y border-white/10 bg-zinc-950/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto grid w-full max-w-7xl grid-cols-1 gap-4 px-6 py-6 sm:grid-cols-2 lg:grid-cols-4 lg:gap-6 lg:py-8\",\n            children: items.map(({ icon: Icon, title, blurb })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3 rounded-2xl border border-white/10 bg-black/30 p-4 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-0.5 flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500/30 to-cyan-500/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"h-5 w-5 text-emerald-300\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-semibold text-white\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-zinc-300\",\n                                    children: blurb\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, title, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/BenefitsStrip.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/BenefitsStrip.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,Phone,ShieldCheck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n// components/Footer.tsx\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto grid w-full max-w-7xl gap-6 px-4 py-10 sm:px-6 lg:px-8 sm:grid-cols-2 md:grid-cols-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 12,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold tracking-wide\",\n                                    children: \"CloudNextAI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-3 text-sm text-zinc-400\",\n                            children: \"AI Security, Made Simple.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Product\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/product\",\n                                        className: \"hover:text-white\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/docs/getting-started\",\n                                        className: \"hover:text-white\",\n                                        children: \"Getting Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/docs/wif-wizard\",\n                                        className: \"hover:text-white\",\n                                        children: \"WIF Wizard\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"hover:text-white\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Company\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/solutions\",\n                                        className: \"hover:text-white\",\n                                        children: \"Solutions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/security\",\n                                        className: \"hover:text-white\",\n                                        children: \"Security\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/contact\",\n                                        className: \"hover:text-white\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-semibold text-white\",\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 space-y-2 text-sm text-zinc-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"hover:text-white\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+15195551234\",\n                                            className: \"hover:text-white\",\n                                            children: \"+****************\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"hover:text-white\",\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_Phone_ShieldCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"hover:text-white\",\n                                            children: \"LinkedIn\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:col-span-2 md:col-span-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-xs text-zinc-500\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" CloudNextAI. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n// components/Hero.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst images = [\n    {\n        src: \"/images/hero-1.png\",\n        alt: \"IAM Health Score Dashboard\"\n    },\n    {\n        src: \"/images/hero-2.png\",\n        alt: \"GitHub → GCP WIF Integration\"\n    },\n    {\n        src: \"/images/hero-3.png\",\n        alt: \"Compliance Export Pack\"\n    }\n];\nfunction Hero() {\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Auto-rotate every 5s\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setIndex((prev)=>(prev + 1) % images.length);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative overflow-hidden bg-gradient-to-br from-black to-zinc-950 py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid items-center gap-10 lg:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mb-4 inline-block rounded-full bg-emerald-500/10 px-3 py-1 text-sm font-semibold text-emerald-400\",\n                                children: \"AI Security\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl\",\n                                children: \"Made Simple\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg text-zinc-300\",\n                                children: \"Replace static keys with short-lived tokens. Get an IAM Health Score. Export HIPAA/SOC 2/PIPEDA evidence in one click.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/contact\",\n                                        className: \"rounded-xl bg-emerald-500/90 px-5 py-3 text-sm font-semibold text-white shadow hover:opacity-95\",\n                                        children: \"Book a Free Assessment\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/docs/getting-started\",\n                                        className: \"rounded-xl border border-white/20 px-5 py-3 text-sm font-semibold text-white hover:bg-white/5\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-80 w-full overflow-hidden rounded-2xl border border-white/10 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: images[index].src,\n                                    alt: images[index].alt,\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIndex((index - 1 + images.length) % images.length),\n                                className: \"absolute left-3 top-1/2 -translate-y-1/2 rounded-full bg-black/40 p-2 text-white hover:bg-black/70\",\n                                \"aria-label\": \"Previous slide\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIndex((index + 1) % images.length),\n                                className: \"absolute right-3 top-1/2 -translate-y-1/2 rounded-full bg-black/40 p-2 text-white hover:bg-black/70\",\n                                \"aria-label\": \"Next slide\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-2\",\n                                children: images.map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIndex(i),\n                                        className: `h-2 w-2 rounded-full ${i === index ? \"bg-emerald-400\" : \"bg-white/30\"}`,\n                                        \"aria-label\": `Go to slide ${i + 1}`\n                                    }, i, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Hero.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu,ShieldCheck,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu,ShieldCheck,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu,ShieldCheck,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Menu,ShieldCheck,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n// components/Navbar.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst NavLink = ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: href,\n        className: \"hover:text-white\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\nfunction Navbar() {\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-40 border-b border-white/10 bg-black/60 backdrop-blur-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto flex h-16 w-full max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold tracking-wide\",\n                                children: \"CloudNextAI\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden items-center gap-6 text-sm text-zinc-300 md:flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/product\",\n                                children: \"Product\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/solutions\",\n                                children: \"Solutions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/pricing\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/docs\",\n                                children: \"Docs\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                href: \"/security\",\n                                children: \"Security\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden items-center gap-3 md:flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/contact\",\n                            className: \"inline-flex items-center gap-2 rounded-xl bg-emerald-500/90 px-3 py-2 text-sm font-semibold text-white shadow hover:opacity-95\",\n                            children: [\n                                \"Book a free assessment \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 36\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"md:hidden\",\n                        \"aria-label\": \"Open menu\",\n                        onClick: ()=>setOpen(true),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-6 w-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40 bg-black/60\",\n                        onClick: ()=>setOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed right-0 top-0 z-50 h-full w-80 border-l border-white/10 bg-zinc-950 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold\",\n                                                children: \"CloudNextAI\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        \"aria-label\": \"Close menu\",\n                                        onClick: ()=>setOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"grid gap-4 text-zinc-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        href: \"/product\",\n                                        children: \"Product\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        href: \"/solutions\",\n                                        children: \"Solutions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        href: \"/pricing\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        href: \"/docs\",\n                                        children: \"Docs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                        href: \"/security\",\n                                        children: \"Security\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/contact\",\n                                        className: \"mt-2 inline-flex items-center justify-center gap-2 rounded-xl bg-emerald-500/90 px-3 py-2 text-sm font-semibold text-white hover:opacity-95\",\n                                        children: [\n                                            \"Free assessment \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Menu_ShieldCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/components/Navbar.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3e2f9963d4d8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbG91ZG5leHRhaS1zaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2ZkMjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzZTJmOTk2M2Q0ZDhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n// app/layout.tsx\n\n\nconst metadata = {\n    metadataBase: new URL(\"https://www.cloudnext.ai\"),\n    title: {\n        default: \"CloudNextAI — AI + Cloud Security Copilot for SMBs\",\n        template: \"%s | CloudNextAI\"\n    },\n    description: \"Secure your AI agents & GCP cloud without hiring a security team. Keyless GitHub→GCP, least privilege IAM, compliant logging.\",\n    keywords: [\n        \"Cloud security\",\n        \"AI security\",\n        \"GCP\",\n        \"Workload Identity Federation\",\n        \"WIF\",\n        \"OIDC\",\n        \"GitHub Actions\",\n        \"SOC 2\",\n        \"HIPAA\",\n        \"PIPEDA\"\n    ],\n    openGraph: {\n        title: \"CloudNextAI — AI + Cloud Security Copilot for SMBs\",\n        description: \"Keyless by default. Short-lived tokens. IAM Health Score. Compliance packs for HIPAA, SOC 2, PIPEDA.\",\n        url: \"https://www.cloudnext.ai\",\n        siteName: \"CloudNextAI\",\n        images: [\n            {\n                url: \"/og.png\",\n                width: 1200,\n                height: 630\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        creator: \"@cloudnextai\"\n    },\n    alternates: {\n        canonical: \"https://www.cloudnext.ai\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    },\n    themeColor: \"#0B0B0B\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-black text-zinc-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"#main\",\n                    className: \"sr-only focus:not-sr-only focus:fixed focus:top-3 focus:left-3 focus:z-50 rounded bg-white/10 px-3 py-2 text-white\",\n                    children: \"Skip to content\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/layout.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();