"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact/route";
exports.ids = ["app/api/contact/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_antonyalphonse_Desktop_Antony_Company_cloudnextai_website_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/contact/route.ts */ \"(rsc)/./app/api/contact/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/Antony/Company/cloudnextai-website/app/api/contact/route.ts\",\n    nextConfigOutput,\n    userland: _Users_antonyalphonse_Desktop_Antony_Company_cloudnextai_website_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/contact/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjb250YWN0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjb250YWN0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY29udGFjdCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRmFudG9ueWFscGhvbnNlJTJGRGVza3RvcCUyRkFudG9ueSUyRkNvbXBhbnklMkZjbG91ZG5leHRhaS13ZWJzaXRlJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRmFudG9ueWFscGhvbnNlJTJGRGVza3RvcCUyRkFudG9ueSUyRkNvbXBhbnklMkZjbG91ZG5leHRhaS13ZWJzaXRlJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUN5QztBQUN0SDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nsb3VkbmV4dGFpLXNpdGUvPzdjYWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL1VzZXJzL2FudG9ueWFscGhvbnNlL0Rlc2t0b3AvQW50b255L0NvbXBhbnkvY2xvdWRuZXh0YWktd2Vic2l0ZS9hcHAvYXBpL2NvbnRhY3Qvcm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbnRhY3Qvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb250YWN0XCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9jb250YWN0L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiL1VzZXJzL2FudG9ueWFscGhvbnNlL0Rlc2t0b3AvQW50b255L0NvbXBhbnkvY2xvdWRuZXh0YWktd2Vic2l0ZS9hcHAvYXBpL2NvbnRhY3Qvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2NvbnRhY3Qvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/contact/route.ts":
/*!**********************************!*\
  !*** ./app/api/contact/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// app/api/contact/route.ts\n\nfunction wantsHTML(req) {\n    const accept = req.headers.get(\"accept\") || \"\";\n    return accept.includes(\"text/html\") && !accept.includes(\"application/json\");\n}\nfunction redirectHome(req, ok) {\n    const url = new URL(req.url);\n    url.pathname = \"/\";\n    url.searchParams.set(\"sent\", ok ? \"1\" : \"0\");\n    // 303 See Other = \"POST succeeded, now go GET this URL\"\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url, {\n        status: 303\n    });\n}\nasync function POST(req) {\n    try {\n        const contentType = req.headers.get(\"content-type\") || \"\";\n        let payload = {};\n        if (contentType.includes(\"application/json\")) {\n            payload = await req.json();\n        } else {\n            const form = await req.formData();\n            payload = Object.fromEntries(form.entries());\n        }\n        const name = (payload.name || \"\").toString();\n        const email = (payload.email || \"\").toString();\n        const company = (payload.company || \"\").toString();\n        const message = (payload.message || \"\").toString();\n        if (!name || !email) {\n            if (wantsHTML(req)) return redirectHome(req, false);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ok: false,\n                error: \"Name and email are required.\"\n            }, {\n                status: 400\n            });\n        }\n        // Send with Resend (no extra deps)\n        const RESEND_KEY = process.env.RESEND_API_KEY;\n        const TO = process.env.CONTACT_TO || \"<EMAIL>\";\n        if (RESEND_KEY) {\n            const subject = `CloudNextAI — New request from ${name}`;\n            const html = `\n        <h2>New Assessment Request</h2>\n        <p><b>Name:</b> ${name}</p>\n        <p><b>Email:</b> ${email}</p>\n        <p><b>Company:</b> ${company}</p>\n        <p><b>Message:</b><br/>${message.replace(/\\n/g, \"<br/>\")}</p>\n      `;\n            await fetch(\"https://api.resend.com/emails\", {\n                method: \"POST\",\n                headers: {\n                    Authorization: `Bearer ${RESEND_KEY}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    from: \"CloudNextAI <<EMAIL>>\",\n                    to: [\n                        TO\n                    ],\n                    subject,\n                    html,\n                    reply_to: email\n                })\n            });\n        } else {\n            console.warn(\"RESEND_API_KEY missing — skipping email send\");\n        }\n        // If the request came from a normal HTML form post, redirect to home with a flag.\n        if (wantsHTML(req)) return redirectHome(req, true);\n        // If it was a fetch/XHR submit, return JSON\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ok: true,\n            message: \"Thanks! We'll get back to you within 1 business day.\"\n        });\n    } catch (err) {\n        console.error(err);\n        if (wantsHTML(req)) return redirectHome(req, false);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ok: false,\n            error: \"Something went wrong.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/contact/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fantonyalphonse%2FDesktop%2FAntony%2FCompany%2Fcloudnextai-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();