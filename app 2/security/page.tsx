import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Security & Compliance" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Security & Compliance</h1>
          <ul className="mt-4 list-disc space-y-2 pl-6 text-zinc-300">
            <li>Keyless by default: Workload Identity & WIF (no static credentials).</li>
            <li>Short‑lived, downscoped tokens for all actions.</li>
            <li>Binary Authorization, Cosign image signing, and SBOM support.</li>
            <li>OpenTelemetry traces and hash‑chained audit ledger.</li>
            <li>HIPAA, SOC 2, and PIPEDA export packs.</li>
          </ul>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}