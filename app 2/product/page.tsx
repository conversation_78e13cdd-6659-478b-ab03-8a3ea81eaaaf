import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { ShieldCheck, LockKeyhole, FileCheck, CheckCircle2 } from "lucide-react";

export const metadata = { title: "Product" };

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

const Feature = ({ title, points, icon }: { title: string; points: string[]; icon: React.ReactNode }) => (
  <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
    <div className="mb-2 flex items-center gap-2 text-lg font-semibold text-white">{icon}<span>{title}</span></div>
    <ul className="space-y-2 text-sm text-zinc-300">{points.map((p,i)=>(<li key={i} className="flex gap-2"><CheckCircle2 className="h-4 w-4 mt-0.5 shrink-0"/>{p}</li>))}</ul>
  </div>
);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">CloudNextAI Product</h1>
          <p className="mt-3 text-zinc-300">One platform combining AI firewall, IAM guardrails, and compliance automation — purpose-built for SMBs.</p>
          <div className="mt-8 grid gap-6 md:grid-cols-3">
            <Feature title="AI Agent Security" icon={<ShieldCheck className="h-5 w-5 text-emerald-400" />} points={[
              "Prompt firewall blocks jailbreaks & data exfil",
              "PII/PHI redaction with GCP DLP",
              "Tool/API allowlisting"
            ]}/>
            <Feature title="GCP IAM Guardrails" icon={<LockKeyhole className="h-5 w-5 text-cyan-400" />} points={[
              "Workload Identity (no static keys)",
              "Short-lived per-action tokens",
              "Credential Access Boundaries (downscoping)"
            ]}/>
            <Feature title="Compliance Copilot" icon={<FileCheck className="h-5 w-5 text-emerald-400" />} points={[
              "Hash-chained evidence ledger",
              "Compliance packs: HIPAA, SOC 2, PIPEDA",
              "Policy versioning, drift detection, approvals"
            ]}/>
          </div>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}