import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const contentType = req.headers.get("content-type") || "";
  let payload: any = {};
  if (contentType.includes("application/json")) {
    payload = await req.json();
  } else if (contentType.includes("application/x-www-form-urlencoded") || contentType.includes("multipart/form-data")) {
    const form = await req.formData();
    payload = Object.fromEntries(form.entries());
  }

  // TODO: send email via SendGrid/Resend, or write to CRM
  console.log("Contact submission:", payload);

  return NextResponse.json({ ok: true, message: "Thanks! We'll get back to you within 1 business day." });
}