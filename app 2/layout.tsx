export const metadata = {
  metadataBase: new URL("https://www.cloudnext.ai"),
  title: {
    default: "CloudNextAI — AI + Cloud Security Copilot for SMBs",
    template: "%s | CloudNextAI"
  },
  description: "Secure your AI agents & GCP cloud without hiring a security team.",
  openGraph: {
    title: "CloudNextAI — AI + Cloud Security Copilot for SMBs",
    description: "Secure your AI agents & GCP cloud without hiring a security team.",
    url: "https://www.cloudnext.ai",
    siteName: "CloudNextAI",
    images: [{ url: "/og.png", width: 1200, height: 630 }],
    locale: "en_US",
    type: "website"
  },
  robots: { index: true, follow: true }
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-black text-zinc-100">{children}</body>
    </html>
  );
}
