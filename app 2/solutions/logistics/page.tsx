import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Logistics" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Logistics</h1>
          <ul className="mt-4 list-disc space-y-2 pl-6 text-zinc-300">
            <li>Mask PII in shipping/customer data flows.</li><li>Just‑in‑time tokens for warehouse/ops automations.</li><li>Drift detection on policies impacting SLAs.</li>
          </ul>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}
