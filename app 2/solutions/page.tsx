import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Link from "next/link";

export const metadata = { title: "Solutions" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Solutions by Industry</h1>
          <p className="mt-3 text-zinc-300">Purpose-built guardrails for regulated SMBs.</p>
          <ul className="mt-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {[
              ["healthcare","Healthcare"],
              ["manufacturing","Manufacturing"],
              ["logistics","Logistics"],
              ["financial-services","Financial Services"]
            ].map(([slug, name]) => (
              <li key={slug} className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
                <h2 className="text-lg font-semibold text-white">{name}</h2>
                <p className="mt-2 text-sm text-zinc-400">Reduce risk and accelerate AI adoption.</p>
                <Link className="mt-4 inline-block text-emerald-400 hover:underline" href={`/solutions/${slug}`}>Learn more →</Link>
              </li>
            ))}
          </ul>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}