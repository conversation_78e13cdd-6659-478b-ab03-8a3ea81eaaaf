import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Healthcare" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Healthcare</h1>
          <ul className="mt-4 list-disc space-y-2 pl-6 text-zinc-300">
            <li>De‑identify PHI before model calls using GCP DLP.</li><li>HIPAA evidence ledger + 1‑click audit PDFs.</li><li>Least‑privilege IAM for clinical and back‑office agents.</li>
          </ul>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}
