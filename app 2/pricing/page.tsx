import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Pricing" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Pricing</h1>
          <p className="mt-3 text-zinc-300">Transparent, SMB-friendly tiers. Add-on: Managed WIF migration package.</p>
          <div className="mt-10 grid gap-6 lg:grid-cols-3">
            {[
              {name:"Starter", price:"$499/mo", points:["3 AI agents","2 GCP projects","Prompt firewall & redaction","Compliance PDFs"]},
              {name:"Growth", price:"$1,499/mo", points:["10 AI agents","5 GCP projects","IAM Health Score","Approval workflows"], featured:true},
              {name:"Enterprise+", price:"Custom", points:["On‑prem control plane","Custom compliance mappings","Dedicated support & SLOs","Security review & hardening"]},
            ].map((t,i)=> (
              <div key={i} className={`rounded-2xl border border-white/10 bg-zinc-900/60 p-6 ${t.featured ? "ring-2 ring-emerald-500/40":""}`}>
                <div className="flex items-center justify-between"><div className="text-xl font-semibold text-white">{t.name}</div>{t.featured && <span className="rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30">Most Popular</span>}</div>
                <div className="mt-2 text-3xl font-bold text-white">{t.price}</div>
                <ul className="mt-4 list-disc space-y-2 pl-6 text-sm text-zinc-300">{t.points.map((p:string,idx:number)=>(<li key={idx}>{p}</li>))}</ul>
                <a href="/#contact" className="mt-6 inline-flex w-full items-center justify-center rounded-xl bg-white px-4 py-2 font-semibold text-black hover:bg-zinc-100">{t.name=="Growth"?"Choose Growth":"Get Started"}</a>
              </div>
            ))}
          </div>
          <div className="mt-10 text-sm text-zinc-400">
            <p>All plans include: Prompt firewall, PII/PHI redaction, WIF Wizard, evidence ledger, and email support.</p>
          </div>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}