import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Link from "next/link";

export const metadata = { title: "Docs" };

const Section = ({ children }: { children: React.ReactNode }) => (<section className="py-16 sm:py-24">{children}</section>);
const Container = ({ children }: { children: React.ReactNode }) => (<div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Documentation</h1>
          <ul className="mt-6 list-disc pl-6 text-zinc-300 space-y-2">
            <li><Link className="text-emerald-400 hover:underline" href="/docs/getting-started">Getting Started</Link></li>
            <li><Link className="text-emerald-400 hover:underline" href="/docs/wif-wizard">WIF Wizard</Link></li>
            <li><Link className="text-emerald-400 hover:underline" href="/docs/gateway-deploy">Gateway Deployment</Link></li>
          </ul>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}