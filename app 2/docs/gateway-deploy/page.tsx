import Navbar from "@/components/Navbar"; import Footer from "@/components/Footer"; export const metadata={title:"Gateway Deployment"}; export default function Page(){return(<main><Navbar/><section className="py-16 sm:py-24"><div className="mx-auto w-full max-w-3xl px-4 sm:px-6 lg:px-8"><h1 className="text-3xl font-bold text-white">Gateway Deployment</h1><p className="mt-4 text-zinc-300">Deploy the FastAPI + OPA gateway into your project with a few commands.</p><pre className="mt-4 rounded-xl bg-zinc-950 p-4 text-sm text-zinc-200 whitespace-pre-wrap"># Placeholder deploy commands
# gcloud run deploy cloudnextai-gateway --image=gcr.io/your/gateway:tag
</pre></div></section><Footer/></main>)}