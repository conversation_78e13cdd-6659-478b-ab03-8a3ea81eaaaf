/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  async headers() {
    // Adjust sources if you add/remove vendors (Calendly, analytics, etc.)
    const csp = `
      default-src 'self';
      img-src 'self' data: blob:;
      script-src 'self' 'unsafe-inline' https://plausible.io;
      connect-src 'self' https://api.resend.com https://plausible.io;
      style-src 'self' 'unsafe-inline';
      font-src 'self' data:;
      frame-src https://calendly.com;
    `.replace(/\s{2,}/g, ' ').trim();

    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'Content-Security-Policy', value: csp },
          { key: 'Strict-Transport-Security', value: 'max-age=63072000; includeSubDomains; preload' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
          { key: 'Permissions-Policy', value: 'camera=(), microphone=(), geolocation=()' },
        ],
      },
    ];
  },
};
module.exports = nextConfig;
