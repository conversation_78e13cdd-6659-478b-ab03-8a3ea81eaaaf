// app/security/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = {
  title: "Security & Compliance",
  description:
    "Keyless by default, short-lived tokens, signed builds, and exportable audit evidence for HIPAA/SOC 2/PIPEDA.",
};

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Security & Compliance</h1>

          <div className="mt-6 grid gap-6 md:grid-cols-2">
            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="text-lg font-semibold text-white">Trust model</div>
              <ul className="mt-2 list-disc space-y-2 pl-6 text-sm text-zinc-300">
                <li>No static credentials (Workload Identity Federation).</li>
                <li>Short-lived, downscoped tokens for every action.</li>
                <li>Binary Authorization and image signing (Cosign).</li>
              </ul>
            </div>

            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="text-lg font-semibold text-white">Observability</div>
              <ul className="mt-2 list-disc space-y-2 pl-6 text-sm text-zinc-300">
                <li>OpenTelemetry traces linked to GitHub runs.</li>
                <li>Hash-chained audit ledger and exportable reports.</li>
                <li>Drift detection on IAM bindings and roles.</li>
              </ul>
            </div>

            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6 md:col-span-2">
              <div className="text-lg font-semibold text-white">Compliance packs</div>
              <p className="mt-2 text-sm text-zinc-300">
                Pre-mapped controls and export templates accelerate audits:
                HIPAA, SOC 2, and PIPEDA (Canada).
              </p>
            </div>
          </div>
        </Container>
      </Section>

      <Footer />
    </main>
  );
}
