"use client";
import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { motion } from "framer-motion";
import { ShieldCheck, KeyRound, FileCheck, Wand2, Gauge, LockKeyhole, ScrollText, CheckCircle2, ArrowRight, Building2, Boxes, Workflow, ClipboardCheck, Radar } from "lucide-react";

const Container = ({ className = "", children }: React.PropsWithChildren<{ className?: string }>) => (
  <div className={`mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 ${className}`}>{children}</div>
);
const Section = ({ id, className = "", children }: React.PropsWithChildren<{ id?: string; className?: string }>) => (
  <section id={id} className={`py-16 sm:py-24 ${className}`}>{children}</section>
);
const featureFade = { hidden: { opacity: 0, y: 8 }, show: { opacity: 1, y: 0, transition: { duration: 0.4 } } };
const Tag = ({ children }: { children: React.ReactNode }) => (<span className="inline-flex items-center rounded-full border border-white/20 bg-white/5 px-3 py-1 text-xs font-medium text-white/90 backdrop-blur-sm">{children}</span>);
const Pill = ({ children }: { children: React.ReactNode }) => (<span className="inline-flex items-center gap-2 rounded-full bg-zinc-900/80 px-3 py-1 text-xs text-zinc-200 ring-1 ring-white/10">{children}</span>);
const Badge = ({ children }: { children: React.ReactNode }) => (<span className="rounded-full bg-emerald-600/15 px-2.5 py-0.5 text-xs font-medium text-emerald-400 ring-1 ring-emerald-500/30">{children}</span>);
const Card = ({ children, className = "" }: React.PropsWithChildren<{ className?: string }>) => (<div className={`rounded-2xl border border-white/10 bg-zinc-900/60 p-6 shadow-[0_0_0_1px_rgba(255,255,255,0.04)] backdrop-blur ${className}`}>{children}</div>);
const CardTitle = ({ children, icon }: { children: React.ReactNode; icon?: React.ReactNode }) => (<div className="mb-2 flex items-center gap-2 text-lg font-semibold text-white">{icon}<span>{children}</span></div>);
const CardItem = ({ children }: { children: React.ReactNode }) => (<li className="flex items-start gap-2 text-sm text-zinc-300"><CheckCircle2 className="mt-0.5 h-4 w-4 shrink-0" />{children}</li>);
const PriceRow = ({ children }: { children: React.ReactNode }) => (<div className="flex items-end gap-1 text-4xl font-bold text-white">{children}</div>);

export default function Page() {
  return (
    <main>
      <Navbar />
      <div className="relative overflow-hidden bg-[radial-gradient(60rem_60rem_at_50%_-20rem,rgba(16,185,129,0.25),transparent),radial-gradient(60rem_60rem_at_-10%_-10%,rgba(34,211,238,0.25),transparent)]">
        <Section className="pb-20 pt-24">
          <Container>
            <div className="grid items-center gap-10 md:grid-cols-2">
              <motion.div initial={{ opacity: 0, y: 12 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
                <Tag>AI + Cloud Security Copilot for SMBs</Tag>
                <h1 className="mt-4 text-4xl font-bold leading-tight text-white sm:text-5xl">Secure your AI agents & cloud — without hiring a security team</h1>
                <p className="mt-4 text-lg text-zinc-300">CloudNextAI protects your AI and GCP workloads from data leaks, IAM misconfigs, and compliance failures. Keyless by default. SMB-friendly. Production ready.</p>
                <div className="mt-6 flex flex-wrap items-center gap-3">
                  <a href="#contact" className="inline-flex items-center gap-2 rounded-xl bg-white px-5 py-2.5 font-semibold text-black shadow hover:bg-zinc-100">Book a free security assessment <ArrowRight className="h-4 w-4" /></a>
                  <a href="/product" className="inline-flex items-center gap-2 rounded-xl border border-white/20 px-5 py-2.5 font-semibold text-white hover:bg-white/5">See how it works</a>
                  <div className="ml-2 flex items-center gap-2 text-xs text-zinc-400">
                    <Pill><LockKeyhole className="h-3.5 w-3.5" /> Keyless IAM (WIF)</Pill>
                    <Pill><Wand2 className="h-3.5 w-3.5" /> Prompt Firewall</Pill>
                    <Pill><ClipboardCheck className="h-3.5 w-3.5" /> Compliance Packs</Pill>
                  </div>
                </div>
              </motion.div>
              <motion.div initial={{ opacity: 0, y: 12 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.1 }}>
                <div className="relative rounded-3xl border border-white/10 bg-zinc-950/60 p-4 shadow-2xl">
                  <div className="grid grid-cols-2 gap-3 sm:gap-4">
                    <Card><CardTitle icon={<ShieldCheck className="h-5 w-5 text-emerald-400" />}>AI Agent Security</CardTitle><ul className="space-y-2 text-zinc-300"><CardItem>Prompt firewall blocks jailbreaks & exfiltration</CardItem><CardItem>PII/PHI redaction via GCP DLP</CardItem><CardItem>Tool allowlisting for APIs/DBs</CardItem></ul></Card>
                    <Card><CardTitle icon={<KeyRound className="h-5 w-5 text-cyan-400" />}>GCP IAM Guardrails</CardTitle><ul className="space-y-2 text-zinc-300"><CardItem>Workload Identity, no static keys</CardItem><CardItem>Short-lived per-action tokens</CardItem><CardItem>Downscoping via Credential Access Boundaries</CardItem></ul></Card>
                    <Card className="col-span-2"><CardTitle icon={<FileCheck className="h-5 w-5 text-emerald-400" />}>Compliance Copilot</CardTitle><ul className="grid gap-2 sm:grid-cols-2"><CardItem>Hash-chained evidence ledger</CardItem><CardItem>HIPAA, SOC 2, PIPEDA 1-click PDFs</CardItem><CardItem>Policy versioning & drift detection</CardItem><CardItem>Approval workflows</CardItem></ul></Card>
                  </div>
                </div>
              </motion.div>
            </div>
          </Container>
        </Section>
      </div>

      {/* Problem/Solution/How/Compliance/Pricing from landing */}
      <Section id="problem">
        <Container>
          <div className="mx-auto max-w-3xl text-center"><h2 className="text-3xl font-bold text-white">The Problem</h2><p className="mt-3 text-zinc-300">SMBs want AI & cloud speed but can’t afford security teams. Result: static keys in repos, over-broad IAM, prompt injection risks, and audit failures.</p></div>
          <div className="mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {[{ title: "Static keys everywhere", icon: <KeyRound className="h-5 w-5" /> },{ title: "Over-broad IAM roles", icon: <Gauge className="h-5 w-5" /> },{ title: "Prompt injection & exfil", icon: <Radar className="h-5 w-5" /> },{ title: "Compliance evidence chaos", icon: <ScrollText className="h-5 w-5" /> }].map((f, i) => (
              <motion.div key={i} initial={{opacity:0,y:8}} whileInView={{opacity:1,y:0}} viewport={{ once: true }} transition={{duration:0.4}} className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
                <div className="flex items-center gap-2 text-white">{f.icon}<h3 className="text-lg font-semibold">{f.title}</h3></div>
                <p className="mt-2 text-sm text-zinc-300">We replace brittle manual controls with opinionated guardrails and automation designed for lean teams.</p>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      <Section id="contact" className="bg-zinc-950/60">
        <Container>
          <div className="mx-auto max-w-3xl text-center"><h2 className="text-3xl font-bold text-white">Book a Free Security Assessment</h2><p className="mt-3 text-zinc-300">Get a quick gap report on IAM, prompts, and compliance readiness for your GCP projects.</p></div>
          <div className="mx-auto mt-8 max-w-xl rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
            <form action="/api/contact" method="post" className="grid gap-4">
              <input name="name" className="rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500" placeholder="Full name" />
              <input name="email" type="email" className="rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500" placeholder="Work email" />
              <input name="company" className="rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500" placeholder="Company" />
              <textarea name="message" rows={4} className="rounded-xl border border-white/10 bg-black/40 px-4 py-3 text-sm text-white placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-emerald-500" placeholder="Tell us about your AI/cloud setup" />
              <button className="inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-5 py-3 font-semibold text-white hover:opacity-95">Request Assessment</button>
              <p className="text-center text-xs text-zinc-500">We’ll reply within 1 business day.</p>
            </form>
          </div>
        </Container>
      </Section>

      <Footer />
    </main>
  );
}
