// app/layout.tsx
import "./globals.css";

export const metadata = {
  metadataBase: new URL("https://www.cloudnext.ai"),
  title: {
    default: "CloudNextAI — AI + Cloud Security Copilot for SMBs",
    template: "%s | CloudNextAI",
  },
  description:
    "Secure your AI agents & GCP cloud without hiring a security team. Keyless GitHub→GCP, least privilege IAM, compliant logging.",
  keywords: [
    "Cloud security",
    "AI security",
    "GCP",
    "Workload Identity Federation",
    "WIF",
    "OIDC",
    "GitHub Actions",
    "SOC 2",
    "HIPAA",
    "PIPEDA",
  ],
  openGraph: {
    title: "CloudNextAI — AI + Cloud Security Copilot for SMBs",
    description:
      "Keyless by default. Short-lived tokens. IAM Health Score. Compliance packs for HIPAA, SOC 2, PIPEDA.",
    url: "https://www.cloudnext.ai",
    siteName: "CloudNextAI",
    images: [{ url: "/og.png", width: 1200, height: 630 }],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    creator: "@cloudnextai",
  },
  alternates: { canonical: "https://www.cloudnext.ai" },
  robots: { index: true, follow: true },
  themeColor: "#0B0B0B",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-black text-zinc-100">
        <a
          href="#main"
          className="sr-only focus:not-sr-only focus:fixed focus:top-3 focus:left-3 focus:z-50 rounded bg-white/10 px-3 py-2 text-white"
        >
          Skip to content
        </a>
        {children}
      </body>
    </html>
  );
}
