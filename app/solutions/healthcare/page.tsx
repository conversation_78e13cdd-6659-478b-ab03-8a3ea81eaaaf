// app/solutions/healthcare/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = {
  title: "Healthcare",
  description:
    "Protect PHI with least-privilege AI workflows, auditable access, and HIPAA mappings.",
};

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Healthcare</h1>
          <p className="mt-3 max-w-3xl text-zinc-300">
            CloudNextAI helps providers and healthtech teams build AI features safely on GCP:
            PHI redaction, zero-trust CI/CD, and HIPAA evidence exports.
          </p>

          <div className="mt-8 grid gap-6 md:grid-cols-2">
            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="text-lg font-semibold text-white">Problem</div>
              <ul className="mt-2 list-disc space-y-2 pl-6 text-sm text-zinc-300">
                <li>Legacy service account keys and broad IAM roles.</li>
                <li>PHI risk in AI prompts and logs.</li>
                <li>Manual evidence collection for HIPAA audits.</li>
              </ul>
            </div>
            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="text-lg font-semibold text-white">Solution</div>
              <ul className="mt-2 list-disc space-y-2 pl-6 text-sm text-zinc-300">
                <li>Keyless GitHub→GCP (WIF) with short-lived tokens.</li>
                <li>Prompt firewall + on-the-fly PHI redaction.</li>
                <li>Signed builds, SBOM, and HIPAA export pack.</li>
              </ul>
            </div>
          </div>

          <div className="mt-8">
            <a
              href="/contact"
              className="inline-flex items-center rounded-xl bg-emerald-500/90 px-4 py-2 font-semibold text-white hover:opacity-95"
            >
              Talk to a healthcare security engineer
            </a>
          </div>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}
