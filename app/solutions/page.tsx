// app/solutions/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Link from "next/link";

export const metadata = {
  title: "Solutions",
  description: "Industry solutions for Healthcare, Financial Services, Manufacturing, and Logistics.",
};

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

const Card = ({
  title,
  bullets,
  href,
}: {
  title: string;
  bullets: string[];
  href: string;
}) => (
  <Link
    href={href}
    className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6 hover:border-emerald-500/40"
  >
    <div className="text-lg font-semibold text-white">{title}</div>
    <ul className="mt-3 space-y-2 text-sm text-zinc-300">
      {bullets.map((b) => (
        <li key={b}>• {b}</li>
      ))}
    </ul>
    <div className="mt-4 text-sm text-emerald-400">Explore →</div>
  </Link>
);

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Solutions</h1>
          <div className="mt-8 grid gap-6 md:grid-cols-2">
            <Card
              title="Healthcare"
              href="/solutions/healthcare"
              bullets={[
                "PHI redaction & access controls",
                "Audit trails for HIPAA",
                "Zero-trust CI/CD for EHR integrations",
              ]}
            />
            <Card
              title="Financial Services"
              href="/solutions/financial-services"
              bullets={[
                "SOX/SOC 2 controls mapping",
                "PII tokenization & prompt firewall",
                "Strong approvals for sensitive ops",
              ]}
            />
            <Card
              title="Manufacturing"
              href="/solutions/manufacturing"
              bullets={[
                "Non-prod → prod separation",
                "Signed images & provenance",
                "Warehouse assistant guardrails",
              ]}
            />
            <Card
              title="Logistics"
              href="/solutions/logistics"
              bullets={[
                "Customer data masking",
                "Least privilege shipping automations",
                "Drift detection & alerting",
              ]}
            />
          </div>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}
