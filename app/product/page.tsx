// app/product/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { CheckCircle2, ShieldCheck, LockKeyhole, FileCheck } from "lucide-react";

export const metadata = {
  title: "Product",
  description:
    "CloudNextAI is a security copilot for GCP and AI agents: keyless GitHub→GCP, least-privilege IAM, redaction, and compliance packs.",
};

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

const Bullet = ({ children }: { children: React.ReactNode }) => (
  <li className="flex gap-2 text-sm text-zinc-300">
    <CheckCircle2 className="mt-0.5 h-4 w-4 shrink-0 text-emerald-400" />
    <span>{children}</span>
  </li>
);

export default function Page() {
  return (
    <main>
      <Navbar />

      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">CloudNextAI Product</h1>
          <p className="mt-3 max-w-3xl text-zinc-300">
            Ship AI-powered apps safely on GCP. We replace static keys with
            <span className="font-semibold text-white"> Workload Identity Federation</span>,
            downscope access, and continuously score your IAM posture.
          </p>

          <div className="mt-10 grid gap-6 md:grid-cols-3">
            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="mb-2 flex items-center gap-2 text-lg font-semibold text-white">
                <ShieldCheck className="h-5 w-5" />
                Keyless by default
              </div>
              <ul className="space-y-2">
                <Bullet>GitHub→GCP via OIDC, no long-lived keys</Bullet>
                <Bullet>Short-lived, downscoped tokens</Bullet>
                <Bullet>Audit trail with OTel traces</Bullet>
              </ul>
            </div>

            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="mb-2 flex items-center gap-2 text-lg font-semibold text-white">
                <LockKeyhole className="h-5 w-5" />
                Least-Privilege IAM
              </div>
              <ul className="space-y-2">
                <Bullet>IAM Health Score with drift detection</Bullet>
                <Bullet>Approval workflows for privilege bumps</Bullet>
                <Bullet>Guardrails for service accounts & keys</Bullet>
              </ul>
            </div>

            <div className="rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
              <div className="mb-2 flex items-center gap-2 text-lg font-semibold text-white">
                <FileCheck className="h-5 w-5" />
                Compliance packs
              </div>
              <ul className="space-y-2">
                <Bullet>HIPAA, SOC 2, PIPEDA export artifacts</Bullet>
                <Bullet>Signed SBOM + Binary Authorization</Bullet>
                <Bullet>PII redaction & prompt firewall</Bullet>
              </ul>
            </div>
          </div>

          <div className="mt-10 rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
            <h2 className="text-xl font-semibold text-white">How it works</h2>
            <ol className="mt-3 list-decimal space-y-2 pl-6 text-sm text-zinc-300">
              <li>Connect GitHub and your GCP org; we create a WIF pool/provider.</li>
              <li>Grant a deployer service account with minimal roles.</li>
              <li>Use our GitHub Action snippet to mint short-lived tokens at build time.</li>
              <li>We continuously analyze IAM for over-permission and stale bindings.</li>
            </ol>
          </div>

          <div className="mt-10 flex flex-wrap items-center gap-4 text-sm text-zinc-400">
            <span>Integrations:</span>
            <span className="rounded-md border border-white/10 px-2 py-1">GitHub</span>
            <span className="rounded-md border border-white/10 px-2 py-1">GCP</span>
            <span className="rounded-md border border-white/10 px-2 py-1">Cloud Run</span>
            <span className="rounded-md border border-white/10 px-2 py-1">Artifact Registry</span>
            <span className="rounded-md border border-white/10 px-2 py-1">OpenTelemetry</span>
          </div>

          <div className="mt-10">
            <a
              href="/contact"
              className="inline-flex items-center rounded-xl bg-emerald-500/90 px-4 py-2 font-semibold text-white hover:opacity-95"
            >
              Book a free 30-min security assessment
            </a>
          </div>
        </Container>
      </Section>

      <Footer />
    </main>
  );
}
