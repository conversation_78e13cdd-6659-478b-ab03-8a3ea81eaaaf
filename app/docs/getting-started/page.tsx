// app/docs/getting-started/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Getting Started", description: "Connect GitHub and GCP, then deploy securely with WIF." };

export default function Page() {
  return (
    <main>
      <Navbar />
      <section className="py-16 sm:py-24">
        <div className="mx-auto w-full max-w-3xl px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-white">Getting Started</h1>
          <ol className="mt-4 list-decimal space-y-3 pl-6 text-zinc-300">
            <li>In CloudNextAI, connect GitHub and your GCP Org/Project.</li>
            <li>We create a Workload Identity pool/provider and a minimal deployer service account.</li>
            <li>Store your provider resource name in GitHub Secrets as <code className="text-white">WIF_PROVIDER</code>.</li>
            <li>Add this step to your workflow:</li>
          </ol>
          <pre className="mt-4 whitespace-pre-wrap rounded-xl bg-zinc-950 p-4 text-sm text-zinc-200">
{`- name: Authenticate to GCP via WIF
  uses: google-github-actions/auth@v2
  with:
    workload_identity_provider: \${{ secrets.WIF_PROVIDER }}
    service_account: <EMAIL>`}
          </pre>
          <ol className="mt-4 list-decimal space-y-3 pl-6 text-zinc-300" start={5}>
            <li>Deploy (Cloud Run / GKE) with short-lived tokens minted at runtime.</li>
          </ol>
        </div>
      </section>
      <Footer />
    </main>
  );
}
