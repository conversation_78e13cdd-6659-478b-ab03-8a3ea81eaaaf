import Navbar from "@/components/Navbar"; import Footer from "@/components/Footer"; export const metadata={title:"WIF Wizard"}; export default function Page(){return(<main><Navbar/><section className="py-16 sm:py-24"><div className="mx-auto w-full max-w-3xl px-4 sm:px-6 lg:px-8"><h1 className="text-3xl font-bold text-white">WIF Wizard</h1><p className="mt-4 text-zinc-300">Generates pool/provider, IAM bindings, and a repo‑specific CI snippet for GitHub Actions using OIDC→STS→short‑lived tokens.</p><pre className="mt-4 rounded-xl bg-zinc-950 p-4 text-sm text-zinc-200 whitespace-pre-wrap"># Example GitHub Action step (placeholder)
- name: Authenticate to GCP via WIF
  uses: google-github-actions/auth@v2
  with:
    workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
    service_account: <EMAIL>
</pre></div></section><Footer/></main>)}