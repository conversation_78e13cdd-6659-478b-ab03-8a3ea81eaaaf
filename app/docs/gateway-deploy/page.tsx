// app/docs/gateway-deploy/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = { title: "Gateway Deployment", description: "Deploy the secure API gateway with signed images and OIDC." };

export default function Page() {
  return (
    <main>
      <Navbar />
      <section className="py-16 sm:py-24">
        <div className="mx-auto w-full max-w-3xl px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-white">Gateway Deployment</h1>
          <p className="mt-4 text-zinc-300">Deploy the gateway to Cloud Run with provenance and short-lived tokens.</p>
          <pre className="mt-4 whitespace-pre-wrap rounded-xl bg-zinc-950 p-4 text-sm text-zinc-200">
{`# Build & push (GitHub runner uses short-lived token via auth@v2)
gcloud builds submit --tag us-docker.pkg.dev/PROJECT/registry/gateway:$(git rev-parse --short HEAD)

# (Optional) sign image with Cosign
cosign sign --key kms://projects/PROJECT/locations/global/keyRings/RING/cryptoKeys/KEY \
  us-docker.pkg.dev/PROJECT/registry/gateway:$(git rev-parse --short HEAD)

# Deploy
gcloud run deploy cloudnextai-gateway \
  --image=us-docker.pkg.dev/PROJECT/registry/gateway:$(git rev-parse --short HEAD) \
  --allow-unauthenticated --region=us-central1 --project=PROJECT`}
          </pre>
        </div>
      </section>
      <Footer />
    </main>
  );
}
