// app/api/contact/route.ts
import { NextRequest, NextResponse } from "next/server";

function wantsHTML(req: NextRequest) {
  const accept = req.headers.get("accept") || "";
  return accept.includes("text/html") && !accept.includes("application/json");
}

function redirectHome(req: NextRequest, ok: boolean) {
  const url = new URL(req.url);
  url.pathname = "/";
  url.searchParams.set("sent", ok ? "1" : "0");
  // 303 See Other = "POST succeeded, now go GET this URL"
  return NextResponse.redirect(url, { status: 303 });
}

export async function POST(req: NextRequest) {
  try {
    const contentType = req.headers.get("content-type") || "";
    let payload: Record<string, any> = {};

    if (contentType.includes("application/json")) {
      payload = await req.json();
    } else {
      const form = await req.formData();
      payload = Object.fromEntries(form.entries());
    }

    const name = (payload.name || "").toString();
    const email = (payload.email || "").toString();
    const company = (payload.company || "").toString();
    const message = (payload.message || "").toString();

    if (!name || !email) {
      if (wantsHTML(req)) return redirectHome(req, false);
      return NextResponse.json({ ok: false, error: "Name and email are required." }, { status: 400 });
    }

    // Send with Resend (no extra deps)
    const RESEND_KEY = process.env.RESEND_API_KEY;
    const TO = process.env.CONTACT_TO || "<EMAIL>";

    if (RESEND_KEY) {
      const subject = `CloudNextAI — New request from ${name}`;
      const html = `
        <h2>New Assessment Request</h2>
        <p><b>Name:</b> ${name}</p>
        <p><b>Email:</b> ${email}</p>
        <p><b>Company:</b> ${company}</p>
        <p><b>Message:</b><br/>${message.replace(/\n/g, "<br/>")}</p>
      `;
      await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${RESEND_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: "CloudNextAI <<EMAIL>>",
          to: [TO],
          subject,
          html,
          reply_to: email,
        }),
      });
    } else {
      console.warn("RESEND_API_KEY missing — skipping email send");
    }

    // If the request came from a normal HTML form post, redirect to home with a flag.
    if (wantsHTML(req)) return redirectHome(req, true);

    // If it was a fetch/XHR submit, return JSON
    return NextResponse.json({ ok: true, message: "Thanks! We'll get back to you within 1 business day." });
  } catch (err) {
    console.error(err);
    if (wantsHTML(req)) return redirectHome(req, false);
    return NextResponse.json({ ok: false, error: "Something went wrong." }, { status: 500 });
  }
}
