// app/contact/page.tsx
"use client";

import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useState } from "react";

export const metadata = {
  title: "Contact",
  description: "Book a free 30-min security assessment with CloudNextAI.",
};

export default function Page() {
  const [status, setStatus] = useState<"idle" | "sending" | "sent" | "error">("idle");

  async function onSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    const form = e.currentTarget;
    const data = Object.fromEntries(new FormData(form).entries()) as Record<string, string>;
    try {
      setStatus("sending");
      const res = await fetch("/api/contact", { method: "POST", body: JSON.stringify(data) });
      const json = await res.json();
      setStatus(json.ok ? "sent" : "error");
      if (json.ok) form.reset();
    } catch {
      setStatus("error");
    }
  }

  return (
    <main>
      <Navbar />
      <section className="py-16 sm:py-24">
        <div className="mx-auto w-full max-w-3xl px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-white">Contact</h1>
          <p className="mt-3 text-zinc-300">
            Tell us a bit about your stack and goals. We’ll reply within 1 business day.
          </p>

          <form onSubmit={onSubmit} className="mt-8 grid gap-4 rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
            <input name="name" required placeholder="Full name" className="rounded-lg bg-black/40 p-3 outline-none ring-1 ring-white/10" />
            <input name="email" required type="email" placeholder="Work email" className="rounded-lg bg-black/40 p-3 outline-none ring-1 ring-white/10" />
            <input name="company" placeholder="Company" className="rounded-lg bg-black/40 p-3 outline-none ring-1 ring-white/10" />
            <textarea name="message" required placeholder="What are you trying to ship / secure?" className="h-28 rounded-lg bg-black/40 p-3 outline-none ring-1 ring-white/10" />
            <button
              disabled={status === "sending"}
              className="rounded-xl bg-emerald-500/90 px-4 py-2 font-semibold text-white hover:opacity-95 disabled:opacity-60"
            >
              {status === "sending" ? "Sending…" : "Book my free assessment"}
            </button>
            {status === "sent" && <p className="text-sm text-emerald-400">Thanks! We’ll be in touch shortly.</p>}
            {status === "error" && <p className="text-sm text-red-400">Something went wrong. Please try again.</p>}
          </form>
        </div>
      </section>
      <Footer />
    </main>
  );
}
