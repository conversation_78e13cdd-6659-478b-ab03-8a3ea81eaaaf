// app/pricing/page.tsx
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata = {
  title: "Pricing",
  description:
    "Transparent, SMB-friendly pricing. Optional managed WIF migration package.",
};

const Section = ({ children }: { children: React.ReactNode }) => (
  <section className="py-16 sm:py-24">{children}</section>
);
const Container = ({ children }: { children: React.ReactNode }) => (
  <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">{children}</div>
);

const plans = [
  {
    name: "Starter",
    price: "$499/mo",
    blurb: "Secure a single team and repo set.",
    points: [
      "3 AI agents / 2 GCP projects",
      "Keyless GitHub→GCP (WIF)",
      "Prompt firewall & redaction",
      "Compliance PDFs",
    ],
  },
  {
    name: "Growth",
    price: "$1,499/mo",
    blurb: "Best for 2–4 product teams.",
    featured: true,
    points: [
      "10 AI agents / 6 GCP projects",
      "IAM Health Score with drift alerts",
      "Approval workflows",
      "SBOM + signed images",
    ],
  },
  {
    name: "Enterprise+",
    price: "Custom",
    blurb: "Advanced controls & SSO integration.",
    points: [
      "On-prem runner hardening",
      "Private control plane",
      "Custom compliance mappings",
      "Dedicated support & SLAs",
    ],
  },
];

export default function Page() {
  return (
    <main>
      <Navbar />
      <Section>
        <Container>
          <h1 className="text-3xl font-bold text-white">Pricing</h1>
          <p className="mt-3 text-zinc-300">
            Transparent, SMB-friendly tiers. Add-on: Managed WIF migration package.
          </p>

          <div className="mt-10 grid gap-6 lg:grid-cols-3">
            {plans.map((p) => (
              <div
                key={p.name}
                className={`rounded-2xl border bg-zinc-900/60 p-6 ${
                  p.featured ? "border-emerald-500/50 shadow" : "border-white/10"
                }`}
              >
                <div className="text-lg font-semibold text-white">{p.name}</div>
                <div className="mt-1 text-3xl font-bold text-white">{p.price}</div>
                <div className="mt-1 text-sm text-zinc-400">{p.blurb}</div>
                <ul className="mt-4 space-y-2 text-sm text-zinc-300">
                  {p.points.map((pt) => (
                    <li key={pt}>• {pt}</li>
                  ))}
                </ul>
                <a
                  href="/contact"
                  className="mt-6 inline-flex w-full items-center justify-center rounded-xl bg-emerald-500/90 px-4 py-2 font-semibold text-white hover:opacity-95"
                >
                  Get started
                </a>
              </div>
            ))}
          </div>

          <div className="mt-16 rounded-2xl border border-white/10 bg-zinc-900/60 p-6">
            <h2 className="text-xl font-semibold text-white">FAQs</h2>
            <ul className="mt-3 space-y-3 text-sm text-zinc-300">
              <li>
                <span className="font-medium text-white">What’s the WIF migration package?</span>
                <br />We stand up pools/providers, bind least-priv roles, and cut over repos in a week.
              </li>
              <li>
                <span className="font-medium text-white">How do you handle compliance?</span>
                <br />Exportable artifacts for HIPAA/SOC 2/PIPEDA and signed build proofs.
              </li>
              <li>
                <span className="font-medium text-white">Do you store secrets?</span>
                <br />No. We remove long-lived keys and issue short-lived tokens on demand.
              </li>
            </ul>
          </div>
        </Container>
      </Section>
      <Footer />
    </main>
  );
}
