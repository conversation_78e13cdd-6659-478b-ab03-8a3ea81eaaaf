// components/Navbar.tsx
"use client";

import Link from "next/link";
import { useState } from "react";
import { ShieldCheck, Menu, X, ArrowRight } from "lucide-react";

const NavLink = ({ href, children }: { href: string; children: React.ReactNode }) => (
  <Link href={href} className="hover:text-white">
    {children}
  </Link>
);

export default function Navbar() {
  const [open, setOpen] = useState(false);

  return (
    <header className="sticky top-0 z-40 border-b border-white/10 bg-black/60 backdrop-blur-md">
      <div className="mx-auto flex h-16 w-full max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
        <Link href="/" className="flex items-center gap-2 text-white">
          <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500">
            <ShieldCheck className="h-5 w-5 text-white" />
          </div>
          <span className="text-sm font-semibold tracking-wide">CloudNextAI</span>
        </Link>

        {/* Desktop */}
        <nav className="hidden items-center gap-6 text-sm text-zinc-300 md:flex">
          <NavLink href="/product">Product</NavLink>
          <NavLink href="/solutions">Solutions</NavLink>
          <NavLink href="/pricing">Pricing</NavLink>
          <NavLink href="/docs">Docs</NavLink>
          <NavLink href="/security">Security</NavLink>
        </nav>

        <div className="hidden items-center gap-3 md:flex">
          <Link
            href="/contact"
            className="inline-flex items-center gap-2 rounded-xl bg-emerald-500/90 px-3 py-2 text-sm font-semibold text-white shadow hover:opacity-95"
          >
            Book a free assessment <ArrowRight className="h-4 w-4" />
          </Link>
        </div>

        {/* Mobile */}
        <button
          className="md:hidden"
          aria-label="Open menu"
          onClick={() => setOpen(true)}
        >
          <Menu className="h-6 w-6 text-white" />
        </button>
      </div>

      {/* Mobile sheet */}
      {open && (
        <div className="md:hidden">
          <div className="fixed inset-0 z-40 bg-black/60" onClick={() => setOpen(false)} />
          <div className="fixed right-0 top-0 z-50 h-full w-80 border-l border-white/10 bg-zinc-950 p-6">
            <div className="mb-6 flex items-center justify-between">
              <div className="flex items-center gap-2 text-white">
                <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500">
                  <ShieldCheck className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-semibold">CloudNextAI</span>
              </div>
              <button aria-label="Close menu" onClick={() => setOpen(false)}>
                <X className="h-6 w-6 text-white" />
              </button>
            </div>

            <nav className="grid gap-4 text-zinc-300">
              <NavLink href="/product">Product</NavLink>
              <NavLink href="/solutions">Solutions</NavLink>
              <NavLink href="/pricing">Pricing</NavLink>
              <NavLink href="/docs">Docs</NavLink>
              <NavLink href="/security">Security</NavLink>
              <Link
                href="/contact"
                className="mt-2 inline-flex items-center justify-center gap-2 rounded-xl bg-emerald-500/90 px-3 py-2 text-sm font-semibold text-white hover:opacity-95"
              >
                Free assessment <ArrowRight className="h-4 w-4" />
              </Link>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
}
