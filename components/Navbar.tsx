"use client";
import Link from "next/link";
import { <PERSON><PERSON>he<PERSON>, <PERSON>R<PERSON> } from "lucide-react";

export default function Navbar() {
  return (
    <header className="sticky top-0 z-40 border-b border-white/10 bg-black/60 backdrop-blur-md">
      <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center gap-2 text-white">
          <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500">
            <ShieldCheck className="h-5 w-5 text-white" />
          </div>
          <span className="text-sm font-semibold tracking-wide">CloudNextAI</span>
        </Link>
        <nav className="hidden gap-6 text-sm text-zinc-300 md:flex">
          <Link href="/product" className="hover:text-white">Product</Link>
          <Link href="/solutions" className="hover:text-white">Solutions</Link>
          <Link href="/pricing" className="hover:text-white">Pricing</Link>
          <Link href="/docs" className="hover:text-white">Docs</Link>
          <Link href="/security" className="hover:text-white">Security</Link>
        </nav>
        <div className="flex items-center gap-3">
          <Link href="/#contact" className="inline-flex items-center gap-2 rounded-xl bg-gradient-to-r from-emerald-500 to-cyan-500 px-4 py-2 text-sm font-semibold text-white shadow hover:opacity-95">
            Book a free assessment <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </header>
  );
}