import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>edin, Mail, Phone } from "lucide-react";
import Link from "next/link";

export default function Footer() {
  return (
    <footer className="border-t border-white/10">
      <div className="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8 grid gap-6 py-10 sm:grid-cols-2 md:grid-cols-4">
        <div>
          <div className="flex items-center gap-2 text-white">
            <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-cyan-500">
              <ShieldCheck className="h-5 w-5 text-white" />
            </div>
            <span className="text-sm font-semibold tracking-wide">CloudNextAI</span>
          </div>
          <p className="mt-3 text-sm text-zinc-400">AI + Cloud Security Copilot for SMBs in Windsor & Detroit.</p>
        </div>
        <div>
          <div className="text-sm font-semibold text-white">Product</div>
          <ul className="mt-2 space-y-2 text-sm text-zinc-300">
            <li><Link href="/product" className="hover:text-white">AI Firewall</Link></li>
            <li><Link href="/product" className="hover:text-white">IAM Guardrails</Link></li>
            <li><Link href="/product" className="hover:text-white">Compliance Copilot</Link></li>
          </ul>
        </div>
        <div>
          <div className="text-sm font-semibold text-white">Company</div>
          <ul className="mt-2 space-y-2 text-sm text-zinc-300">
            <li><Link href="/pricing" className="hover:text-white">Pricing</Link></li>
            <li><Link href="/#contact" className="hover:text-white">Contact</Link></li>
          </ul>
        </div>
        <div>
          <div className="text-sm font-semibold text-white">Contact</div>
          <ul className="mt-2 space-y-2 text-sm text-zinc-300">
            <li className="flex items-center gap-2"><Mail className="h-4 w-4" /><a href="mailto:<EMAIL>" className="hover:text-white"><EMAIL></a></li>
            <li className="flex items-center gap-2"><Phone className="h-4 w-4" /><a href="#" className="hover:text-white">+1 (555) 123‑4567</a></li>
            <li className="flex items-center gap-2"><Github className="h-4 w-4" /><a href="#" className="hover:text-white">GitHub</a></li>
            <li className="flex items-center gap-2"><Linkedin className="h-4 w-4" /><a href="#" className="hover:text-white">LinkedIn</a></li>
          </ul>
        </div>
        <div className="sm:col-span-2 md:col-span-4">
          <p className="text-center text-xs text-zinc-500">© {new Date().getFullYear()} CloudNextAI. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}