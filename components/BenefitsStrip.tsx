// components/BenefitsStrip.tsx
import { Shield<PERSON>heck, KeyRound, FileCheck2, Activity } from "lucide-react";

const items = [
  {
    icon: KeyRound,
    title: "Keyless by default",
    blurb: "Short-lived tokens via Workload Identity Federation.",
  },
  {
    icon: ShieldCheck,
    title: "Least-privilege IAM",
    blurb: "Downscoped access with continuous drift detection.",
  },
  {
    icon: FileCheck2,
    title: "Audit & compliance",
    blurb: "Exportable HIPAA • SOC 2 • PIPEDA evidence.",
  },
  {
    icon: Activity,
    title: "Production-ready",
    blurb: "Signed builds, provenance, and OTel tracing.",
  },
];

export default function BenefitsStrip() {
  return (
    <section
      aria-label="Key benefits"
      className="border-y border-white/10 bg-zinc-950/60"
    >
      <div className="mx-auto grid w-full max-w-7xl grid-cols-1 gap-4 px-6 py-6 sm:grid-cols-2 lg:grid-cols-4 lg:gap-6 lg:py-8">
        {items.map(({ icon: Icon, title, blurb }) => (
          <div
            key={title}
            className="flex items-start gap-3 rounded-2xl border border-white/10 bg-black/30 p-4 shadow-sm"
          >
            <div className="mt-0.5 flex h-9 w-9 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500/30 to-cyan-500/30">
              <Icon className="h-5 w-5 text-emerald-300" aria-hidden="true" />
            </div>
            <div>
              <div className="text-sm font-semibold text-white">{title}</div>
              <div className="text-sm text-zinc-300">{blurb}</div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
