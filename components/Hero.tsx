// components/Hero.tsx
"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";

const images = [
  { src: "/images/hero-1.png", alt: "IAM Health Score Dashboard" },
  { src: "/images/hero-2.png", alt: "GitHub → GCP WIF Integration" },
  { src: "/images/hero-3.png", alt: "Compliance Export Pack" },
];

export default function Hero() {
  const [index, setIndex] = useState(0);

  // Auto-rotate every 5s
  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev + 1) % images.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-black to-zinc-950 py-20">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="grid items-center gap-10 lg:grid-cols-2">
          {/* Left text */}
          <div>
            <span className="mb-4 inline-block rounded-full bg-emerald-500/10 px-3 py-1 text-sm font-semibold text-emerald-400">
              AI Security
            </span>
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              Made Simple
            </h1>
            <p className="mt-6 text-lg text-zinc-300">
              Replace static keys with short-lived tokens. Get an IAM Health
              Score. Export HIPAA/SOC 2/PIPEDA evidence in one click.
            </p>
            <div className="mt-8 flex gap-4">
              <a
                href="/contact"
                className="rounded-xl bg-emerald-500/90 px-5 py-3 text-sm font-semibold text-white shadow hover:opacity-95"
              >
                Book a Free Assessment
              </a>
              <a
                href="/docs/getting-started"
                className="rounded-xl border border-white/20 px-5 py-3 text-sm font-semibold text-white hover:bg-white/5"
              >
                Get Started
              </a>
            </div>
          </div>

          {/* Right carousel */}
          <div className="relative">
            <div className="relative h-80 w-full overflow-hidden rounded-2xl border border-white/10 shadow-lg">
              <Image
                src={images[index].src}
                alt={images[index].alt}
                fill
                className="object-cover"
                priority
              />
            </div>

            {/* Controls */}
            <button
              onClick={() => setIndex((index - 1 + images.length) % images.length)}
              className="absolute left-3 top-1/2 -translate-y-1/2 rounded-full bg-black/40 p-2 text-white hover:bg-black/70"
              aria-label="Previous slide"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={() => setIndex((index + 1) % images.length)}
              className="absolute right-3 top-1/2 -translate-y-1/2 rounded-full bg-black/40 p-2 text-white hover:bg-black/70"
              aria-label="Next slide"
            >
              <ChevronRight className="h-5 w-5" />
            </button>

            {/* Dots */}
            <div className="absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-2">
              {images.map((_, i) => (
                <button
                  key={i}
                  onClick={() => setIndex(i)}
                  className={`h-2 w-2 rounded-full ${
                    i === index ? "bg-emerald-400" : "bg-white/30"
                  }`}
                  aria-label={`Go to slide ${i + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
